{"version": 3, "file": "lang/summernote-ja-JP.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,IADF;AAEJC,QAAAA,MAAM,EAAE,IAFJ;AAGJC,QAAAA,SAAS,EAAE,IAHP;AAIJC,QAAAA,KAAK,EAAE,KAJH;AAKJC,QAAAA,MAAM,EAAE,KALJ;AAMJC,QAAAA,IAAI,EAAE,MANF;AAOJC,QAAAA,aAAa,EAAE,OAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,MAAM,EAAE,MAFH;AAGLC,QAAAA,UAAU,EAAE,KAHP;AAILC,QAAAA,UAAU,EAAE,KAJP;AAKLC,QAAAA,aAAa,EAAE,KALV;AAMLC,QAAAA,SAAS,EAAE,KANN;AAOLC,QAAAA,UAAU,EAAE,KAPP;AAQLC,QAAAA,SAAS,EAAE,MARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,kBAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,WAfZ;AAgBLC,QAAAA,eAAe,EAAE,mBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,cAlBA;AAmBLC,QAAAA,MAAM,EAAE,SAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,SAAS,EAAE,OAFN;AAGLpB,QAAAA,MAAM,EAAE,MAHH;AAILgB,QAAAA,GAAG,EAAE,QAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,KADF;AAEJtB,QAAAA,MAAM,EAAE,OAFJ;AAGJuB,QAAAA,MAAM,EAAE,OAHJ;AAIJC,QAAAA,IAAI,EAAE,IAJF;AAKJC,QAAAA,aAAa,EAAE,QALX;AAMJT,QAAAA,GAAG,EAAE,cAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,WAAW,EAAE,QAFR;AAGLC,QAAAA,WAAW,EAAE,QAHR;AAILC,QAAAA,UAAU,EAAE,QAJP;AAKLC,QAAAA,WAAW,EAAE,QALR;AAMLC,QAAAA,MAAM,EAAE,MANH;AAOLC,QAAAA,MAAM,EAAE,MAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,IAFE;AAGLC,QAAAA,UAAU,EAAE,IAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,MALC;AAMLC,QAAAA,EAAE,EAAE,MANC;AAOLC,QAAAA,EAAE,EAAE,MAPC;AAQLC,QAAAA,EAAE,EAAE,MARC;AASLC,QAAAA,EAAE,EAAE,MATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,OADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,KADC;AAEPC,QAAAA,UAAU,EAAE,SAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,IADF;AAETC,QAAAA,OAAO,EAAE,KAFA;AAGTC,QAAAA,MAAM,EAAE,KAHC;AAITC,QAAAA,IAAI,EAAE,KAJG;AAKTC,QAAAA,MAAM,EAAE,MALC;AAMTC,QAAAA,KAAK,EAAE,KANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,MADH;AAELC,QAAAA,IAAI,EAAE,OAFD;AAGLC,QAAAA,UAAU,EAAE,KAHP;AAILC,QAAAA,UAAU,EAAE,KAJP;AAKLC,QAAAA,WAAW,EAAE,IALR;AAMLC,QAAAA,cAAc,EAAE,OANX;AAOLC,QAAAA,KAAK,EAAE,IAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,SADH;AAERC,QAAAA,KAAK,EAAE,KAFC;AAGRC,QAAAA,cAAc,EAAE,UAHR;AAIRC,QAAAA,MAAM,EAAE,OAJA;AAKRC,QAAAA,mBAAmB,EAAE,UALb;AAMRC,QAAAA,aAAa,EAAE,UANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,MADf;AAEJ,gBAAQ,aAFJ;AAGJ,gBAAQ,cAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,MALL;AAMJ,gBAAQ,KANJ;AAOJ,kBAAU,IAPN;AAQJ,qBAAa,IART;AASJ,yBAAiB,OATb;AAUJ,wBAAgB,OAVZ;AAWJ,uBAAe,KAXX;AAYJ,yBAAiB,OAZb;AAaJ,wBAAgB,KAbZ;AAcJ,uBAAe,QAdX;AAeJ,+BAAuB,SAfnB;AAgBJ,6BAAqB,UAhBjB;AAiBJ,mBAAW,gBAjBP;AAkBJ,kBAAU,cAlBN;AAmBJ,sBAAc,aAnBV;AAoBJ,oBAAY,MApBR;AAqBJ,oBAAY,MArBR;AAsBJ,oBAAY,MAtBR;AAuBJ,oBAAY,MAvBR;AAwBJ,oBAAY,MAxBR;AAyBJ,oBAAY,MAzBR;AA0BJ,gCAAwB,iBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,MADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ja-JP.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'ja-JP': {\n      font: {\n        bold: '太字',\n        italic: '斜体',\n        underline: '下線',\n        clear: 'クリア',\n        height: '文字高',\n        name: 'フォント',\n        strikethrough: '取り消し線',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: '大きさ',\n      },\n      image: {\n        image: '画像',\n        insert: '画像挿入',\n        resizeFull: '最大化',\n        resizeHalf: '1/2',\n        resizeQuarter: '1/4',\n        floatLeft: '左寄せ',\n        floatRight: '右寄せ',\n        floatNone: '寄せ解除',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'ここに画像をドラッグしてください',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: '画像ファイルを選ぶ',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'URLから画像を挿入する',\n        remove: '画像を削除する',\n        original: 'Original',\n      },\n      video: {\n        video: '動画',\n        videoLink: '動画リンク',\n        insert: '動画挿入',\n        url: '動画のURL',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion, Youku)',\n      },\n      link: {\n        link: 'リンク',\n        insert: 'リンク挿入',\n        unlink: 'リンク解除',\n        edit: '編集',\n        textToDisplay: 'リンク文字列',\n        url: 'URLを入力してください',\n        openInNewWindow: '新しいウィンドウで開く',\n      },\n      table: {\n        table: 'テーブル',\n        addRowAbove: '行を上に追加',\n        addRowBelow: '行を下に追加',\n        addColLeft: '列を左に追加',\n        addColRight: '列を右に追加',\n        delRow: '行を削除',\n        delCol: '列を削除',\n        delTable: 'テーブルを削除',\n      },\n      hr: {\n        insert: '水平線の挿入',\n      },\n      style: {\n        style: 'スタイル',\n        p: '標準',\n        blockquote: '引用',\n        pre: 'コード',\n        h1: '見出し1',\n        h2: '見出し2',\n        h3: '見出し3',\n        h4: '見出し4',\n        h5: '見出し5',\n        h6: '見出し6',\n      },\n      lists: {\n        unordered: '通常リスト',\n        ordered: '番号リスト',\n      },\n      options: {\n        help: 'ヘルプ',\n        fullscreen: 'フルスクリーン',\n        codeview: 'コード表示',\n      },\n      paragraph: {\n        paragraph: '文章',\n        outdent: '字上げ',\n        indent: '字下げ',\n        left: '左寄せ',\n        center: '中央寄せ',\n        right: '右寄せ',\n        justify: '均等割付',\n      },\n      color: {\n        recent: '現在の色',\n        more: 'もっと見る',\n        background: '背景色',\n        foreground: '文字色',\n        transparent: '透明',\n        setTransparent: '透明にする',\n        reset: '標準',\n        resetToDefault: '標準に戻す',\n      },\n      shortcut: {\n        shortcuts: 'ショートカット',\n        close: '閉じる',\n        textFormatting: '文字フォーマット',\n        action: 'アクション',\n        paragraphFormatting: '文章フォーマット',\n        documentStyle: 'ドキュメント形式',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': '改行挿入',\n        'undo': '一旦、行った操作を戻す',\n        'redo': '最後のコマンドをやり直す',\n        'tab': 'Tab',\n        'untab': 'タブ戻し',\n        'bold': '太文字',\n        'italic': '斜体',\n        'underline': '下線',\n        'strikethrough': '取り消し線',\n        'removeFormat': '装飾を戻す',\n        'justifyLeft': '左寄せ',\n        'justifyCenter': '真ん中寄せ',\n        'justifyRight': '右寄せ',\n        'justifyFull': 'すべてを整列',\n        'insertUnorderedList': '行頭に●を挿入',\n        'insertOrderedList': '行頭に番号を挿入',\n        'outdent': '字下げを戻す（アウトデント）',\n        'indent': '字下げする（インデント）',\n        'formatPara': '段落(P tag)指定',\n        'formatH1': 'H1指定',\n        'formatH2': 'H2指定',\n        'formatH3': 'H3指定',\n        'formatH4': 'H4指定',\n        'formatH5': 'H5指定',\n        'formatH6': 'H6指定',\n        'insertHorizontalRule': '&lt;hr /&gt;を挿入',\n        'linkDialog.show': 'リンク挿入',\n      },\n      history: {\n        undo: '元に戻す',\n        redo: 'やり直す',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}