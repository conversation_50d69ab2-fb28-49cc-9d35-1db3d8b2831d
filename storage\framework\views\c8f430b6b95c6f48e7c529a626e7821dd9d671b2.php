<!doctype html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" data-bs-theme="light">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo $__env->yieldContent('title', config('app.name', 'Laravel')); ?></title>
    
    <!-- CSS files -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/css/tabler.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg" rel="stylesheet"/>
    
    <!-- Custom CSS -->
    <style>
        /* Theme transition for smooth switching */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Theme toggle button styling */
        .theme-toggle {
            cursor: pointer;
            border: none;
            background: none;
            padding: 0.5rem;
            border-radius: var(--tblr-border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--tblr-body-color);
            transition: background-color 0.2s ease, transform 0.15s ease;
        }

        .theme-toggle:hover {
            background-color: var(--tblr-secondary-bg);
        }

        .theme-toggle:active {
            transform: scale(0.95);
        }

        /* Dynamic icon visibility based on theme */
        .theme-icon-light {
            display: block;
        }

        .theme-icon-dark {
            display: none;
        }

        [data-bs-theme="dark"] .theme-icon-light {
            display: none;
        }

        [data-bs-theme="dark"] .theme-icon-dark {
            display: block;
        }

        /* Ensure proper styling for cards and components */
        .card {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
            box-shadow: var(--tblr-box-shadow);
        }

        /* Navbar styling */
        .navbar {
            background-color: var(--tblr-bg-surface);
            border-bottom: 1px solid var(--tblr-border-color);
        }

        .navbar .navbar-brand,
        .navbar .nav-link {
            color: var(--tblr-body-color);
        }

        /* Form controls */
        .form-control {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
            color: var(--tblr-body-color);
        }

        .form-control:focus {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-primary);
            box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);
        }

        /* Buttons */
        .btn-outline-primary,
        .btn-outline-secondary,
        .btn-outline-info {
            background-color: var(--tblr-bg-surface);
            color: var(--tblr-body-color);
            border-color: var(--tblr-border-color);
        }

        .btn-outline-primary:hover,
        .btn-outline-secondary:hover,
        .btn-outline-info:hover {
            background-color: var(--tblr-secondary-bg);
        }

        /* Dropdown menus */
        .dropdown-menu {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
            box-shadow: var(--tblr-box-shadow-lg);
        }

        .dropdown-item {
            color: var(--tblr-body-color);
        }

        .dropdown-item:hover {
            background-color: var(--tblr-secondary-bg);
        }

        /* Tables */
        .table {
            color: var(--tblr-body-color);
        }

        .table th,
        .table td {
            border-color: var(--tblr-border-color);
        }

        /* Modals */
        .modal-content {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
        }

        .modal-header {
            border-bottom-color: var(--tblr-border-color);
        }

        .modal-footer {
            border-top-color: var(--tblr-border-color);
        }

        /* List groups */
        .list-group-item {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
            color: var(--tblr-body-color);
        }

        /* Alerts */
        .alert {
            border-color: var(--tblr-border-color);
        }

        /* Pagination */
        .page-link {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
            color: var(--tblr-body-color);
        }

        .page-link:hover {
            background-color: var(--tblr-secondary-bg);
            border-color: var(--tblr-border-color);
        }

        .page-item.active .page-link {
            background-color: var(--tblr-primary);
            border-color: var(--tblr-primary);
        }

        /* Progress bars */
        .progress {
            background-color: var(--tblr-secondary-bg);
        }

        /* Tooltips and popovers */
        .tooltip-inner {
            background-color: var(--tblr-bg-surface);
            color: var(--tblr-body-color);
        }

        .popover {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
        }

        .popover-body {
            color: var(--tblr-body-color);
        }

        /* Additional fixes for better dark mode support */
        .text-muted {
            color: var(--tblr-secondary-color) !important;
        }

        .page-header {
            background-color: var(--tblr-body-bg);
            border-bottom: 1px solid var(--tblr-border-color);
        }

        .avatar {
            border: 2px solid var(--tblr-border-color);
        }

        /* Ensure icons inherit proper colors */
        .icon {
            color: inherit;
        }
        
        /* Hide icons based on theme */
        [data-theme="light"] .theme-icon-dark,
        [data-theme="dark"] .theme-icon-light {
            display: none;
        }

        /* Sidebar specific styles */
        .navbar-vertical {
            width: 15rem;
            position: fixed !important;
            top: 0;
            left: 0;
            bottom: 0;
            z-index: 1030;
            background-color: #182433;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            overflow-y: auto;
            box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .navbar-vertical .navbar-brand {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #fff;
            font-weight: 600;
        }

        .navbar-vertical .navbar-brand a {
            color: #fff;
            text-decoration: none;
        }

        .navbar-vertical .navbar-nav {
            flex-direction: column;
            padding: 0;
        }

        .navbar-vertical .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            border-radius: 0;
            margin: 0;
        }

        .navbar-vertical .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .navbar-vertical .nav-link.active {
            background-color: var(--tblr-primary);
            color: #fff;
        }

        .navbar-vertical .nav-link-icon {
            margin-right: 0.75rem;
            width: 1.25rem;
            height: 1.25rem;
            flex-shrink: 0;
        }

        .navbar-vertical .theme-toggle {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1.5rem;
            text-align: left;
            width: 100%;
            display: flex;
            align-items: center;
        }

        .navbar-vertical .theme-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        /* Page wrapper adjustment for sidebar */
        .page-wrapper {
            margin-left: 15rem;
            min-height: 100vh;
            background-color: var(--tblr-body-bg);
        }

        /* Ensure content doesn't get hidden behind sidebar */
        .page-body {
            padding-top: 1rem;
        }

        /* Mobile responsive */
        @media (max-width: 991.98px) {
            .navbar-vertical {
                position: fixed !important;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 280px;
            }

            .navbar-vertical.show {
                transform: translateX(0);
            }

            .page-wrapper {
                margin-left: 0;
            }

            /* Overlay for mobile sidebar */
            .navbar-vertical.show::before {
                content: '';
                position: fixed;
                top: 0;
                left: 280px;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: -1;
            }
        }

        /* Navbar toggler for mobile */
        .navbar-vertical .navbar-toggler {
            position: absolute;
            top: 1rem;
            right: 1rem;
            z-index: 1031;
            color: rgba(255, 255, 255, 0.8);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .navbar-vertical .navbar-toggler:hover {
            color: #fff;
            border-color: rgba(255, 255, 255, 0.5);
        }

        /* Smooth transitions */
        .navbar-vertical .nav-link,
        .navbar-vertical .theme-toggle {
            transition: all 0.2s ease;
        }

        /* Better spacing for sidebar content */
        .navbar-vertical .navbar-collapse {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 80px);
        }

        .navbar-vertical .mt-auto {
            margin-top: auto !important;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 1rem;
        }
    </style>
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <div class="page">
        <!-- Sidebar -->
        <aside class="navbar navbar-vertical navbar-expand-lg" data-bs-theme="dark">
            <div class="container-fluid">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar-menu" aria-controls="sidebar-menu" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <h1 class="navbar-brand navbar-brand-autodark">
                    <a href="<?php echo e(url('/')); ?>">
                        <?php echo e(config('app.name', 'Laravel')); ?>

                    </a>
                </h1>

                <div class="navbar-nav flex-row d-lg-none">
                    <!-- Theme Toggle for mobile -->
                    <div class="nav-item">
                        <button class="theme-toggle nav-link" onclick="toggleTheme()" title="Toggle theme">
                            <!-- Light mode icon (sun) - shown in light mode -->
                            <svg class="theme-icon-light" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="5"></circle>
                                <line x1="12" y1="1" x2="12" y2="3"></line>
                                <line x1="12" y1="21" x2="12" y2="23"></line>
                                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                                <line x1="1" y1="12" x2="3" y2="12"></line>
                                <line x1="21" y1="12" x2="23" y2="12"></line>
                                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                            </svg>
                            <!-- Dark mode icon (moon) - shown in dark mode -->
                            <svg class="theme-icon-dark" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-link-icon">
                                <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M9 7 h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3"></path>
                                    <path d="M9 15h3l8.5 -8.5a1.5 1.5 0 0 0 -3 -3l-8.5 8.5v3"></path>
                                    <path d="M16 5l3 3"></path>
                                </svg>
                            </span>
                        </a>
                    </div>
                </div>

                <div class="collapse navbar-collapse" id="sidebar-menu">
                    <ul class="navbar-nav pt-lg-3">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(url('/')); ?>">
                                <span class="nav-link-icon d-md-none d-lg-inline-block">
                                    <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <polyline points="5 12 3 12 12 3 21 12 19 12"></polyline>
                                        <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"></path>
                                        <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"></path>
                                    </svg>
                                </span>
                                <span class="nav-link-title">Home</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(url('/tabler')); ?>">
                                <span class="nav-link-icon d-md-none d-lg-inline-block">
                                    <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <rect x="4" y="4" width="6" height="6" rx="1"></rect>
                                        <rect x="14" y="4" width="6" height="6" rx="1"></rect>
                                        <rect x="4" y="14" width="6" height="6" rx="1"></rect>
                                        <rect x="14" y="14" width="6" height="6" rx="1"></rect>
                                    </svg>
                                </span>
                                <span class="nav-link-title">Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(url('/components')); ?>">
                                <span class="nav-link-icon d-md-none d-lg-inline-block">
                                    <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <rect x="4" y="4" width="16" height="16" rx="2"></rect>
                                        <rect x="9" y="9" width="6" height="6"></rect>
                                        <line x1="9" y1="1" x2="9" y2="4"></line>
                                        <line x1="15" y1="1" x2="15" y2="4"></line>
                                        <line x1="9" y1="20" x2="9" y2="23"></line>
                                        <line x1="15" y1="20" x2="15" y2="23"></line>
                                        <line x1="20" y1="9" x2="23" y2="9"></line>
                                        <line x1="20" y1="14" x2="23" y2="14"></line>
                                        <line x1="1" y1="9" x2="4" y2="9"></line>
                                        <line x1="1" y1="14" x2="4" y2="14"></line>
                                    </svg>
                                </span>
                                <span class="nav-link-title">Components</span>
                            </a>
                        </li>
                    </ul>

                    <!-- Bottom section for desktop theme toggle and user -->
                    <div class="mt-auto d-none d-lg-block">
                        <div class="navbar-nav">
                            <div class="nav-item">
                                <button class="theme-toggle nav-link w-100 text-start" onclick="toggleTheme()" title="Toggle theme">
                                    <!-- Light mode icon (sun) - shown in light mode -->
                                    <span class="nav-link-icon">
                                        <svg class="theme-icon-light" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="12" cy="12" r="5"></circle>
                                            <line x1="12" y1="1" x2="12" y2="3"></line>
                                            <line x1="12" y1="21" x2="12" y2="23"></line>
                                            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                                            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                                            <line x1="1" y1="12" x2="3" y2="12"></line>
                                            <line x1="21" y1="12" x2="23" y2="12"></line>
                                            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                                            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                                        </svg>
                                        <!-- Dark mode icon (moon) - shown in dark mode -->
                                        <svg class="theme-icon-dark" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z"></path>
                                        </svg>
                                    </span>
                                    <span class="nav-link-title">Toggle Theme</span>
                                </button>
                            </div>

                            <div class="nav-item">
                                <a href="#" class="nav-link">
                                    <span class="nav-link-icon">
                                        <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <path d="M9 7 h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3"></path>
                                            <path d="M9 15h3l8.5 -8.5a1.5 1.5 0 0 0 -3 -3l-8.5 8.5v3"></path>
                                            <path d="M16 5l3 3"></path>
                                        </svg>
                                    </span>
                                    <span class="nav-link-title">Demo User</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
        
        <!-- Page wrapper -->
        <div class="page-wrapper">
            <!-- Page header -->
            <?php if (! empty(trim($__env->yieldContent('page-header')))): ?>
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            <?php echo $__env->yieldContent('page-header'); ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Page body -->
            <div class="page-body">
                <div class="container-xl">
                    <?php if(session('success')): ?>
                    <div class="alert alert-success alert-dismissible" role="alert">
                        <div class="d-flex">
                            <div>
                                <svg class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M5 12l5 5l10 -10"></path>
                                </svg>
                            </div>
                            <div><?php echo e(session('success')); ?></div>
                        </div>
                        <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                    </div>
                    <?php endif; ?>
                    
                    <?php if(session('error')): ?>
                    <div class="alert alert-danger alert-dismissible" role="alert">
                        <div class="d-flex">
                            <div>
                                <svg class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M12 9v2m0 4v.01"></path>
                                    <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                                </svg>
                            </div>
                            <div><?php echo e(session('error')); ?></div>
                        </div>
                        <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                    </div>
                    <?php endif; ?>
                    
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </div>
            
            <!-- Page footer -->
            <footer class="footer footer-transparent d-print-none">
                <div class="container-xl">
                    <div class="row text-center align-items-center flex-row-reverse">
                        <div class="col-lg-auto ms-lg-auto">
                            <ul class="list-inline list-inline-dots mb-0">
                                <li class="list-inline-item">
                                    <a href="https://tabler.io" target="_blank" class="link-secondary">
                                        Powered by Tabler
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                            <ul class="list-inline list-inline-dots mb-0">
                                <li class="list-inline-item">
                                    Copyright &copy; <?php echo e(date('Y')); ?>

                                    <a href="<?php echo e(url('/')); ?>" class="link-secondary"><?php echo e(config('app.name')); ?></a>.
                                    All rights reserved.
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- Tabler Core -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/js/tabler.min.js"></script>
    
    <!-- Theme Toggle Script -->
    <script>
        // Theme management using Bootstrap's data-bs-theme
        function getStoredTheme() {
            return localStorage.getItem('theme') || 'light';
        }

        function setStoredTheme(theme) {
            localStorage.setItem('theme', theme);
        }

        function setTheme(theme) {
            // Set theme on both html and body elements for maximum compatibility
            document.documentElement.setAttribute('data-bs-theme', theme);
            document.body.setAttribute('data-bs-theme', theme);
            setStoredTheme(theme);

            // Update button title
            const toggleButton = document.querySelector('.theme-toggle');
            if (toggleButton) {
                toggleButton.title = theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode';
            }

            // Dispatch custom event for other components to listen
            window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));

            console.log('Theme set to:', theme); // Debug log
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-bs-theme') || 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);

            // Add visual feedback with better animation
            const toggleButton = document.querySelector('.theme-toggle');
            if (toggleButton) {
                toggleButton.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    toggleButton.style.transform = 'scale(1)';
                }, 100);
            }
        }

        // Get preferred theme from system or storage
        function getPreferredTheme() {
            const storedTheme = getStoredTheme();
            if (storedTheme) {
                return storedTheme;
            }

            // Check system preference
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                return 'dark';
            }

            return 'light';
        }

        // Initialize theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const preferredTheme = getPreferredTheme();
            setTheme(preferredTheme);

            console.log('Theme initialized:', preferredTheme); // Debug log
        });

        // Listen for system theme changes
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

            const handleThemeChange = (e) => {
                // Only auto-switch if user hasn't manually set a preference
                if (!localStorage.getItem('theme')) {
                    setTheme(e.matches ? 'dark' : 'light');
                }
            };

            // Use modern addEventListener if available
            if (mediaQuery.addEventListener) {
                mediaQuery.addEventListener('change', handleThemeChange);
            } else {
                // Fallback for older browsers
                mediaQuery.addListener(handleThemeChange);
            }
        }

        // Keyboard shortcut for theme toggle (Ctrl/Cmd + Shift + T)
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                toggleTheme();
            }
        });

        // Expose functions globally for debugging
        window.themeUtils = {
            getStoredTheme,
            setTheme,
            toggleTheme,
            getPreferredTheme
        };

        // Sidebar toggle functionality for mobile
        function initializeSidebar() {
            const sidebarToggler = document.querySelector('.navbar-vertical .navbar-toggler');
            const sidebar = document.querySelector('.navbar-vertical');
            const sidebarCollapse = document.querySelector('#sidebar-menu');

            if (sidebarToggler && sidebar) {
                sidebarToggler.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    sidebar.classList.toggle('show');
                });

                // Close sidebar when clicking outside on mobile
                document.addEventListener('click', function(e) {
                    if (window.innerWidth <= 991.98) {
                        if (!sidebar.contains(e.target) && sidebar.classList.contains('show')) {
                            sidebar.classList.remove('show');
                        }
                    }
                });

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 991.98) {
                        sidebar.classList.remove('show');
                    }
                });

                // Handle Bootstrap collapse for mobile
                if (sidebarCollapse) {
                    sidebarCollapse.addEventListener('show.bs.collapse', function() {
                        sidebar.classList.add('show');
                    });

                    sidebarCollapse.addEventListener('hide.bs.collapse', function() {
                        sidebar.classList.remove('show');
                    });
                }
            }

            // Set active nav link based on current URL
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.navbar-vertical .nav-link:not(.theme-toggle)');

            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href) {
                    // Remove any existing active classes
                    link.classList.remove('active');

                    // Check if this link matches current path
                    if (href === currentPath ||
                        (currentPath === '/' && href.endsWith('/')) ||
                        (href !== '/' && currentPath.startsWith(href))) {
                        link.classList.add('active');
                    }
                }
            });
        }

        // Initialize sidebar after DOM is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeSidebar);
        } else {
            initializeSidebar();
        }
    </script>
    
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\regkan\resources\views/layouts/app.blade.php ENDPATH**/ ?>