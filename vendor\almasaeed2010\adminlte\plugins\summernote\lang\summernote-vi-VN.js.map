{"version": 3, "file": "lang/summernote-vi-VN.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,QADF;AAEJC,QAAAA,MAAM,EAAE,YAFJ;AAGJC,QAAAA,SAAS,EAAE,WAHP;AAIJC,QAAAA,KAAK,EAAE,cAJH;AAKJC,QAAAA,MAAM,EAAE,gBALJ;AAMJC,QAAAA,IAAI,EAAE,WANF;AAOJC,QAAAA,aAAa,EAAE,YAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,UADF;AAELC,QAAAA,MAAM,EAAE,MAFH;AAGLC,QAAAA,UAAU,EAAE,MAHP;AAILC,QAAAA,UAAU,EAAE,KAJP;AAKLC,QAAAA,aAAa,EAAE,KALV;AAMLC,QAAAA,SAAS,EAAE,cANN;AAOLC,QAAAA,UAAU,EAAE,cAPP;AAQLC,QAAAA,SAAS,EAAE,YARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,oBAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,cAfZ;AAgBLC,QAAAA,eAAe,EAAE,mBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,KAlBA;AAmBLC,QAAAA,MAAM,EAAE,KAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,gBAFN;AAGLpB,QAAAA,MAAM,EAAE,YAHH;AAILgB,QAAAA,GAAG,EAAE,KAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,WAFJ;AAGJuB,QAAAA,MAAM,EAAE,SAHJ;AAIJC,QAAAA,IAAI,EAAE,KAJF;AAKJC,QAAAA,aAAa,EAAE,kBALX;AAMJT,QAAAA,GAAG,EAAE,KAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,WAAW,EAAE,qBAFR;AAGLC,QAAAA,WAAW,EAAE,qBAHR;AAILC,QAAAA,UAAU,EAAE,mBAJP;AAKLC,QAAAA,WAAW,EAAE,mBALR;AAMLC,QAAAA,MAAM,EAAE,UANH;AAOLC,QAAAA,MAAM,EAAE,SAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,UADF;AAELC,QAAAA,CAAC,EAAE,YAFE;AAGLC,QAAAA,UAAU,EAAE,YAHP;AAILC,QAAAA,GAAG,EAAE,SAJA;AAKLC,QAAAA,EAAE,EAAE,IALC;AAMLC,QAAAA,EAAE,EAAE,IANC;AAOLC,QAAAA,EAAE,EAAE,IAPC;AAQLC,QAAAA,EAAE,EAAE,IARC;AASLC,QAAAA,EAAE,EAAE,IATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,mBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,UADC;AAEPC,QAAAA,UAAU,EAAE,eAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,SADF;AAETC,QAAAA,OAAO,EAAE,gBAFA;AAGTC,QAAAA,MAAM,EAAE,gBAHC;AAITC,QAAAA,IAAI,EAAE,WAJG;AAKTC,QAAAA,MAAM,EAAE,WALC;AAMTC,QAAAA,KAAK,EAAE,WANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,SADH;AAELC,QAAAA,IAAI,EAAE,SAFD;AAGLC,QAAAA,UAAU,EAAE,SAHP;AAILC,QAAAA,UAAU,EAAE,SAJP;AAKLC,QAAAA,WAAW,EAAE,YALR;AAMLC,QAAAA,cAAc,EAAE,gBANX;AAOLC,QAAAA,KAAK,EAAE,eAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,UADH;AAERC,QAAAA,KAAK,EAAE,MAFC;AAGRC,QAAAA,cAAc,EAAE,mBAHR;AAIRC,QAAAA,MAAM,EAAE,WAJA;AAKRC,QAAAA,mBAAmB,EAAE,WALb;AAMRC,QAAAA,aAAa,EAAE,cANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,aADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,gBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-vi-VN.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'vi-VN': {\n      font: {\n        bold: 'In Đậm',\n        italic: 'In Nghiêng',\n        underline: 'Gạch dưới',\n        clear: 'Bỏ định dạng',\n        height: '<PERSON><PERSON>u cao dòng',\n        name: '<PERSON><PERSON><PERSON> chữ',\n        strikethrough: '<PERSON><PERSON><PERSON> ngang',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Cỡ chữ',\n      },\n      image: {\n        image: 'Hình ảnh',\n        insert: 'Chèn',\n        resizeFull: '100%',\n        resizeHalf: '50%',\n        resizeQuarter: '25%',\n        floatLeft: 'Trôi về trái',\n        floatRight: 'Trôi về phải',\n        floatNone: 'Không trôi',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Thả Ảnh ở vùng này',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Chọn từ File',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'URL',\n        remove: 'Xóa',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Link đến Video',\n        insert: 'Chèn Video',\n        url: 'URL',\n        providers: '(Hỗ trợ YouTube, Vimeo, Vine, Instagram, DailyMotion và Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Chèn Link',\n        unlink: 'Gỡ Link',\n        edit: 'Sửa',\n        textToDisplay: 'Văn bản hiển thị',\n        url: 'URL',\n        openInNewWindow: 'Mở ở Cửa sổ mới',\n      },\n      table: {\n        table: 'Bảng',\n        addRowAbove: 'Chèn dòng phía trên',\n        addRowBelow: 'Chèn dòng phía dưới',\n        addColLeft: 'Chèn cột bên trái',\n        addColRight: 'Chèn cột bên phải',\n        delRow: 'Xóa dòng',\n        delCol: 'Xóa cột',\n        delTable: 'Xóa bảng',\n      },\n      hr: {\n        insert: 'Chèn',\n      },\n      style: {\n        style: 'Kiểu chữ',\n        p: 'Chữ thường',\n        blockquote: 'Đoạn trích',\n        pre: 'Mã Code',\n        h1: 'H1',\n        h2: 'H2',\n        h3: 'H3',\n        h4: 'H4',\n        h5: 'H5',\n        h6: 'H6',\n      },\n      lists: {\n        unordered: 'Liệt kê danh sách',\n        ordered: 'Liệt kê theo thứ tự',\n      },\n      options: {\n        help: 'Trợ giúp',\n        fullscreen: 'Toàn Màn hình',\n        codeview: 'Xem Code',\n      },\n      paragraph: {\n        paragraph: 'Canh lề',\n        outdent: 'Dịch sang trái',\n        indent: 'Dịch sang phải',\n        left: 'Canh trái',\n        center: 'Canh giữa',\n        right: 'Canh phải',\n        justify: 'Canh đều',\n      },\n      color: {\n        recent: 'Màu chữ',\n        more: 'Mở rộng',\n        background: 'Màu nền',\n        foreground: 'Màu chữ',\n        transparent: 'trong suốt',\n        setTransparent: 'Nền trong suốt',\n        reset: 'Thiết lập lại',\n        resetToDefault: 'Trở lại ban đầu',\n      },\n      shortcut: {\n        shortcuts: 'Phím tắt',\n        close: 'Đóng',\n        textFormatting: 'Định dạng Văn bản',\n        action: 'Hành động',\n        paragraphFormatting: 'Định dạng',\n        documentStyle: 'Kiểu văn bản',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Chèn đo văn',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Lùi lại',\n        redo: 'Làm lại',\n      },\n      specialChar: {\n        specialChar: 'KÝ TỰ ĐẶC BIỆT',\n        select: 'Chọn ký tự đặc biệt',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}