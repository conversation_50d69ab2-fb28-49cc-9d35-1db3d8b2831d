<?php $__env->startSection('title', 'UI Components - Tabler.io'); ?>

<?php $__env->startSection('page-header'); ?>
<div class="page-pretitle">
    Tabler.io
</div>
<h2 class="page-title">
    UI Components Demo
</h2>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row row-deck row-cards">
    <!-- Buttons -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Buttons</h3>
            </div>
            <div class="card-body">
                <div class="btn-list">
                    <button type="button" class="btn btn-primary">Primary</button>
                    <button type="button" class="btn btn-secondary">Secondary</button>
                    <button type="button" class="btn btn-success">Success</button>
                    <button type="button" class="btn btn-warning">Warning</button>
                    <button type="button" class="btn btn-danger">Danger</button>
                    <button type="button" class="btn btn-info">Info</button>
                    <button type="button" class="btn btn-light">Light</button>
                    <button type="button" class="btn btn-dark">Dark</button>
                </div>
                <div class="btn-list mt-3">
                    <button type="button" class="btn btn-outline-primary">Outline Primary</button>
                    <button type="button" class="btn btn-outline-secondary">Outline Secondary</button>
                    <button type="button" class="btn btn-outline-success">Outline Success</button>
                    <button type="button" class="btn btn-outline-warning">Outline Warning</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cards -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Card Example</h3>
            </div>
            <div class="card-body">
                <p>This is a sample card content. Cards are flexible and extensible content containers.</p>
                <p class="text-muted">They include options for headers and footers, a wide variety of content, contextual background colors, and powerful display options.</p>
            </div>
            <div class="card-footer">
                <button class="btn btn-primary">Action</button>
                <button class="btn btn-outline-secondary">Cancel</button>
            </div>
        </div>
    </div>
    
    <!-- Alerts -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Alerts</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success" role="alert">
                    <div class="d-flex">
                        <div>
                            <svg class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M5 12l5 5l10 -10"></path>
                            </svg>
                        </div>
                        <div>Success alert with icon</div>
                    </div>
                </div>
                
                <div class="alert alert-warning" role="alert">
                    <div class="d-flex">
                        <div>
                            <svg class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M12 9v2m0 4v.01"></path>
                                <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                            </svg>
                        </div>
                        <div>Warning alert with icon</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Form Elements -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Form Elements</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Text Input</label>
                            <input type="text" class="form-control" placeholder="Enter text">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Select</label>
                            <select class="form-select">
                                <option>Option 1</option>
                                <option>Option 2</option>
                                <option>Option 3</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Textarea</label>
                            <textarea class="form-control" rows="3" placeholder="Enter text"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-check">
                                <input class="form-check-input" type="checkbox">
                                <span class="form-check-label">Checkbox option</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tables -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Table Example</h3>
            </div>
            <div class="table-responsive">
                <table class="table table-vcenter card-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Role</th>
                            <th class="w-1"></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>John Doe</td>
                            <td class="text-muted"><EMAIL></td>
                            <td><span class="badge bg-success me-1"></span> Active</td>
                            <td>Admin</td>
                            <td>
                                <a href="#" class="btn btn-sm btn-outline-primary">Edit</a>
                            </td>
                        </tr>
                        <tr>
                            <td>Jane Smith</td>
                            <td class="text-muted"><EMAIL></td>
                            <td><span class="badge bg-warning me-1"></span> Pending</td>
                            <td>User</td>
                            <td>
                                <a href="#" class="btn btn-sm btn-outline-primary">Edit</a>
                            </td>
                        </tr>
                        <tr>
                            <td>Bob Johnson</td>
                            <td class="text-muted"><EMAIL></td>
                            <td><span class="badge bg-danger me-1"></span> Inactive</td>
                            <td>User</td>
                            <td>
                                <a href="#" class="btn btn-sm btn-outline-primary">Edit</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\regkan\resources\views/components.blade.php ENDPATH**/ ?>