{"version": 3, "file": "popper.js", "sources": ["../src/utils/isBrowser.js", "../src/utils/debounce.js", "../src/utils/isFunction.js", "../src/utils/getStyleComputedProperty.js", "../src/utils/getParentNode.js", "../src/utils/getScrollParent.js", "../src/utils/getReferenceNode.js", "../src/utils/isIE.js", "../src/utils/getOffsetParent.js", "../src/utils/isOffsetContainer.js", "../src/utils/getRoot.js", "../src/utils/findCommonOffsetParent.js", "../src/utils/getScroll.js", "../src/utils/includeScroll.js", "../src/utils/getBordersSize.js", "../src/utils/getWindowSizes.js", "../src/utils/getClientRect.js", "../src/utils/getBoundingClientRect.js", "../src/utils/getOffsetRectRelativeToArbitraryNode.js", "../src/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../src/utils/isFixed.js", "../src/utils/getFixedPositionOffsetParent.js", "../src/utils/getBoundaries.js", "../src/utils/computeAutoPlacement.js", "../src/utils/getReferenceOffsets.js", "../src/utils/getOuterSizes.js", "../src/utils/getOppositePlacement.js", "../src/utils/getPopperOffsets.js", "../src/utils/find.js", "../src/utils/findIndex.js", "../src/utils/runModifiers.js", "../src/methods/update.js", "../src/utils/isModifierEnabled.js", "../src/utils/getSupportedPropertyName.js", "../src/methods/destroy.js", "../src/utils/getWindow.js", "../src/utils/setupEventListeners.js", "../src/methods/enableEventListeners.js", "../src/utils/removeEventListeners.js", "../src/methods/disableEventListeners.js", "../src/utils/isNumeric.js", "../src/utils/setStyles.js", "../src/utils/setAttributes.js", "../src/modifiers/applyStyle.js", "../src/utils/getRoundedOffsets.js", "../src/modifiers/computeStyle.js", "../src/utils/isModifierRequired.js", "../src/modifiers/arrow.js", "../src/utils/getOppositeVariation.js", "../src/methods/placements.js", "../src/utils/clockwise.js", "../src/modifiers/flip.js", "../src/modifiers/keepTogether.js", "../src/modifiers/offset.js", "../src/modifiers/preventOverflow.js", "../src/modifiers/shift.js", "../src/modifiers/hide.js", "../src/modifiers/inner.js", "../src/modifiers/index.js", "../src/methods/defaults.js", "../src/index.js"], "sourcesContent": ["export default typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n", "import isBrowser from './isBrowser';\n\nconst timeoutDuration = (function(){\n  const longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}());\n\nexport function microtaskDebounce(fn) {\n  let called = false\n  return () => {\n    if (called) {\n      return\n    }\n    called = true\n    window.Promise.resolve().then(() => {\n      called = false\n      fn()\n    })\n  }\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nconst supportsMicroTasks = isBrowser && window.Promise\n\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsMicroTasks\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const window = element.ownerDocument.defaultView;\n  const css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body\n    case '#document':\n      return element.body\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nexport default function getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n", "import isBrowser from './isBrowser';\n\nconst isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nconst isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nexport default function isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  const noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  let offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    parseFloat(styles[`border${sideA}Width`]) +\n    parseFloat(styles[`border${sideB}Width`])\n  );\n}\n", "import isIE from './isIE';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE(10)\n      ? (parseInt(html[`offset${axis}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]))\n    : 0 \n  );\n}\n\nexport default function getWindowSizes(document) {\n  const body = document.body;\n  const html = document.documentElement;\n  const computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE from './isIE';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    }\n    else {\n      rect = element.getBoundingClientRect();\n    }\n  }\n  catch(e){}\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  const width =\n    sizes.width || element.clientWidth || result.width;\n  const height =\n    sizes.height || element.clientHeight || result.height;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE from './isIE';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  const isIE10 = runIsIE(10);\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if(fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = parseFloat(styles.marginTop);\n    const marginLeft = parseFloat(styles.marginLeft);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10 && !fixedPosition\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  const parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nexport default function getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n   if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  let el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getReferenceNode from './getReferenceNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement,\n  fixedPosition = false\n) {\n  // NOTE: 1 DOM access here\n\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport' ) {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  }\n\n  else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent,\n      fixedPosition\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes(popper.ownerDocument);\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  const isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0; \n  boundaries.top += isPaddingNumber ? padding : padding.top || 0; \n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0; \n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0; \n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\nimport getReferenceNode from './getReferenceNode';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference, fixedPosition = null) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const window = element.ownerDocument.defaultView;\n  const styles = window.getComputedStyle(element);\n  const x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  const y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier['function']) { // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "import computeAutoPlacement from '../utils/computeAutoPlacement';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nexport default function update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  let data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {},\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(\n    this.state,\n    this.popper,\n    this.reference,\n    this.options.positionFixed\n  );\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(\n    this.options.placement,\n    data.offsets.reference,\n    this.popper,\n    this.reference,\n    this.options.modifiers.flip.boundariesElement,\n    this.options.modifiers.flip.padding\n  );\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(\n    this.popper,\n    data.offsets.reference,\n    data.placement\n  );\n\n  data.offsets.popper.position = this.options.positionFixed\n    ? 'fixed'\n    : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "import isModifierEnabled from '../utils/isModifierEnabled';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nexport default function destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n", "/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nexport default function getWindow(element) {\n  const ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n", "import getScrollParent from './getScrollParent';\nimport getWindow from './getWindow';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import setupEventListeners from '../utils/setupEventListeners';\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nexport default function enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(\n      this.reference,\n      this.options,\n      this.state,\n      this.scheduleUpdate\n    );\n  }\n}\n", "import getWindow from './getWindow';\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import removeEventListeners from '../utils/removeEventListeners';\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nexport default function disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import setStyles from '../utils/setStyles';\nimport setAttributes from '../utils/setAttributes';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nexport default function applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nexport function applyStyleOnLoad(\n  reference,\n  popper,\n  options,\n  modifierOptions,\n  state\n) {\n  // compute reference element offsets\n  const referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  const placement = computeAutoPlacement(\n    options.placement,\n    referenceOffsets,\n    popper,\n    reference,\n    options.modifiers.flip.boundariesElement,\n    options.modifiers.flip.padding\n  );\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n", "/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nexport default function getRoundedOffsets(data, shouldRound) {\n  const { popper, reference } = data.offsets;\n  const { round, floor } = Math;\n  const noRound = v => v;\n  \n  const referenceWidth = round(reference.width);\n  const popperWidth = round(popper.width);\n  \n  const isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  const isVariation = data.placement.indexOf('-') !== -1;\n  const sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  const bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n\n  const horizontalToInteger = !shouldRound\n    ? noRound\n    : isVertical || isVariation || sameWidthParity\n    ? round\n    : floor;\n  const verticalToInteger = !shouldRound ? noRound : round;\n\n  return {\n    left: horizontalToInteger(\n      bothOddWidth && !isVariation && shouldRound\n        ? popper.left - 1\n        : popper.left\n    ),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right),\n  };\n}\n", "import getSupportedPropertyName from '../utils/getSupportedPropertyName';\nimport find from '../utils/find';\nimport getOffsetParent from '../utils/getOffsetParent';\nimport getBoundingClientRect from '../utils/getBoundingClientRect';\nimport getRoundedOffsets from '../utils/getRoundedOffsets';\nimport isBrowser from '../utils/isBrowser';\n\nconst isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeStyle(data, options) {\n  const { x, y } = options;\n  const { popper } = data.offsets;\n\n  // Remove this legacy support in Popper.js v2\n  const legacyGpuAccelerationOption = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'applyStyle'\n  ).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn(\n      'WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!'\n    );\n  }\n  const gpuAcceleration =\n    legacyGpuAccelerationOption !== undefined\n      ? legacyGpuAccelerationOption\n      : options.gpuAcceleration;\n\n  const offsetParent = getOffsetParent(data.instance.popper);\n  const offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  const styles = {\n    position: popper.position,\n  };\n\n  const offsets = getRoundedOffsets(\n    data,\n    window.devicePixelRatio < 2 || !isFirefox\n  );\n\n  const sideA = x === 'bottom' ? 'top' : 'bottom';\n  const sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  const prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  let left, top;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = `translate3d(${left}px, ${top}px, 0)`;\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    const invertTop = sideA === 'bottom' ? -1 : 1;\n    const invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = `${sideA}, ${sideB}`;\n  }\n\n  // Attributes\n  const attributes = {\n    'x-placement': data.placement,\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = { ...attributes, ...data.attributes };\n  data.styles = { ...styles, ...data.styles };\n  data.arrowStyles = { ...data.offsets.arrow, ...data.arrowStyles };\n\n  return data;\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOuterSizes from '../utils/getOuterSizes';\nimport isModifierRequired from '../utils/isModifierRequired';\nimport getStyleComputedProperty from '../utils/getStyleComputedProperty';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function arrow(data, options) {\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  let arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn(\n        'WARNING: `arrow.element` must be child of its popper element!'\n      );\n      return data;\n    }\n  }\n\n  const placement = data.placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  const len = isVertical ? 'height' : 'width';\n  const sideCapitalized = isVertical ? 'Top' : 'Left';\n  const side = sideCapitalized.toLowerCase();\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -=\n      popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] +=\n      reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  const center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  const css = getStyleComputedProperty(data.instance.popper);\n  const popperMarginSide = parseFloat(css[`margin${sideCapitalized}`]);\n  const popperBorderSide = parseFloat(css[`border${sideCapitalized}Width`]);\n  let sideValue =\n    center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = {\n    [side]: Math.round(sideValue),\n    [altSide]: '', // make sure to unset any eventual altSide value from the DOM node\n  };\n\n  return data;\n}\n", "/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nexport default function getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n", "/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nexport default [\n  'auto-start',\n  'auto',\n  'auto-end',\n  'top-start',\n  'top',\n  'top-end',\n  'right-start',\n  'right',\n  'right-end',\n  'bottom-end',\n  'bottom',\n  'bottom-start',\n  'left-end',\n  'left',\n  'left-start',\n];\n", "import placements from '../methods/placements';\n\n// Get rid of `auto` `auto-start` and `auto-end`\nconst validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nexport default function clockwise(placement, counter = false) {\n  const index = validPlacements.indexOf(placement);\n  const arr = validPlacements\n    .slice(index + 1)\n    .concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n", "import getOppositePlacement from '../utils/getOppositePlacement';\nimport getOppositeVariation from '../utils/getOppositeVariation';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\nimport getBoundaries from '../utils/getBoundaries';\nimport isModifierEnabled from '../utils/isModifierEnabled';\nimport clockwise from '../utils/clockwise';\n\nconst BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise',\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    options.boundariesElement,\n    data.positionFixed\n  );\n\n  let placement = data.placement.split('-')[0];\n  let placementOpposite = getOppositePlacement(placement);\n  let variation = data.placement.split('-')[1] || '';\n\n  let flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach((step, index) => {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    const popperOffsets = data.offsets.popper;\n    const refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    const floor = Math.floor;\n    const overlapsRef =\n      (placement === 'left' &&\n        floor(popperOffsets.right) > floor(refOffsets.left)) ||\n      (placement === 'right' &&\n        floor(popperOffsets.left) < floor(refOffsets.right)) ||\n      (placement === 'top' &&\n        floor(popperOffsets.bottom) > floor(refOffsets.top)) ||\n      (placement === 'bottom' &&\n        floor(popperOffsets.top) < floor(refOffsets.bottom));\n\n    const overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    const overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    const overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    const overflowsBottom =\n      floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    const overflowsBoundaries =\n      (placement === 'left' && overflowsLeft) ||\n      (placement === 'right' && overflowsRight) ||\n      (placement === 'top' && overflowsTop) ||\n      (placement === 'bottom' && overflowsBottom);\n\n    // flip the variation if required\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    const flippedVariationByRef =\n      !!options.flipVariations &&\n      ((isVertical && variation === 'start' && overflowsLeft) ||\n        (isVertical && variation === 'end' && overflowsRight) ||\n        (!isVertical && variation === 'start' && overflowsTop) ||\n        (!isVertical && variation === 'end' && overflowsBottom));\n\n    // flips variation if popper content overflows boundaries\n    const flippedVariationByContent =\n      !!options.flipVariationsByContent &&\n      ((isVertical && variation === 'start' && overflowsRight) ||\n        (isVertical && variation === 'end' && overflowsLeft) ||\n        (!isVertical && variation === 'start' && overflowsBottom) ||\n        (!isVertical && variation === 'end' && overflowsTop));\n\n    const flippedVariation = flippedVariationByRef || flippedVariationByContent;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = {\n        ...data.offsets.popper,\n        ...getPopperOffsets(\n          data.instance.popper,\n          data.offsets.reference,\n          data.placement\n        ),\n      };\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function keepTogether(data) {\n  const { popper, reference } = data.offsets;\n  const placement = data.placement.split('-')[0];\n  const floor = Math.floor;\n  const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  const side = isVertical ? 'right' : 'bottom';\n  const opSide = isVertical ? 'left' : 'top';\n  const measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] =\n      floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n", "import isNumeric from '../utils/isNumeric';\nimport getClientRect from '../utils/getClientRect';\nimport find from '../utils/find';\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nexport function toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  const split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  const value = +split[1];\n  const unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    let element;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    const rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    let size;\n    if (unit === 'vh') {\n      size = Math.max(\n        document.documentElement.clientHeight,\n        window.innerHeight || 0\n      );\n    } else {\n      size = Math.max(\n        document.documentElement.clientWidth,\n        window.innerWidth || 0\n      );\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nexport function parseOffset(\n  offset,\n  popperOffsets,\n  referenceOffsets,\n  basePlacement\n) {\n  const offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  const useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  const fragments = offset.split(/(\\+|\\-)/).map(frag => frag.trim());\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  const divider = fragments.indexOf(\n    find(fragments, frag => frag.search(/,|\\s/) !== -1)\n  );\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn(\n      'Offsets separated by white space(s) are deprecated, use a comma (,) instead.'\n    );\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  const splitRegex = /\\s*,\\s*|\\s+/;\n  let ops = divider !== -1\n    ? [\n        fragments\n          .slice(0, divider)\n          .concat([fragments[divider].split(splitRegex)[0]]),\n        [fragments[divider].split(splitRegex)[1]].concat(\n          fragments.slice(divider + 1)\n        ),\n      ]\n    : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map((op, index) => {\n    // Most of the units rely on the orientation of the popper\n    const measurement = (index === 1 ? !useHeight : useHeight)\n      ? 'height'\n      : 'width';\n    let mergeWithPrevious = false;\n    return (\n      op\n        // This aggregates any `+` or `-` sign that aren't considered operators\n        // e.g.: 10 + +5 => [10, +, +5]\n        .reduce((a, b) => {\n          if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n            a[a.length - 1] = b;\n            mergeWithPrevious = true;\n            return a;\n          } else if (mergeWithPrevious) {\n            a[a.length - 1] += b;\n            mergeWithPrevious = false;\n            return a;\n          } else {\n            return a.concat(b);\n          }\n        }, [])\n        // Here we convert the string values into number values (in px)\n        .map(str => toValue(str, measurement, popperOffsets, referenceOffsets))\n    );\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach((op, index) => {\n    op.forEach((frag, index2) => {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nexport default function offset(data, { offset }) {\n  const { placement, offsets: { popper, reference } } = data;\n  const basePlacement = placement.split('-')[0];\n\n  let offsets;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n", "import getOffsetParent from '../utils/getOffsetParent';\nimport getBoundaries from '../utils/getBoundaries';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function preventOverflow(data, options) {\n  let boundariesElement =\n    options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  const transformProp = getSupportedPropertyName('transform');\n  const popperStyles = data.instance.popper.style; // assignment to help minification\n  const { top, left, [transformProp]: transform } = popperStyles;\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    boundariesElement,\n    data.positionFixed\n  );\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  const order = options.priority;\n  let popper = data.offsets.popper;\n\n  const check = {\n    primary(placement) {\n      let value = popper[placement];\n      if (\n        popper[placement] < boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return { [placement]: value };\n    },\n    secondary(placement) {\n      const mainSide = placement === 'right' ? 'left' : 'top';\n      let value = popper[mainSide];\n      if (\n        popper[placement] > boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.min(\n          popper[mainSide],\n          boundaries[placement] -\n            (placement === 'right' ? popper.width : popper.height)\n        );\n      }\n      return { [mainSide]: value };\n    },\n  };\n\n  order.forEach(placement => {\n    const side =\n      ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = { ...popper, ...check[side](placement) };\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function shift(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    const { reference, popper } = data.offsets;\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n\n    const shiftOffsets = {\n      start: { [side]: reference[side] },\n      end: {\n        [side]: reference[side] + reference[measurement] - popper[measurement],\n      },\n    };\n\n    data.offsets.popper = { ...popper, ...shiftOffsets[shiftvariation] };\n  }\n\n  return data;\n}\n", "import isModifierRequired from '../utils/isModifierRequired';\nimport find from '../utils/find';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  const refRect = data.offsets.reference;\n  const bound = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'preventOverflow'\n  ).boundaries;\n\n  if (\n    refRect.bottom < bound.top ||\n    refRect.left > bound.right ||\n    refRect.top > bound.bottom ||\n    refRect.right < bound.left\n  ) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOppositePlacement from '../utils/getOppositePlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function inner(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  const subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] =\n    reference[basePlacement] -\n    (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n", "import applyStyle, { applyStyleOnLoad } from './applyStyle';\nimport computeStyle from './computeStyle';\nimport arrow from './arrow';\nimport flip from './flip';\nimport keepTogether from './keepTogether';\nimport offset from './offset';\nimport preventOverflow from './preventOverflow';\nimport shift from './shift';\nimport hide from './hide';\nimport inner from './inner';\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nexport default {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift,\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0,\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent',\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether,\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]',\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false,\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner,\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide,\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right',\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined,\n  },\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n", "import modifiers from '../modifiers/index';\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nexport default {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: () => {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: () => {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers,\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n", "// Utils\nimport debounce from './utils/debounce';\nimport isFunction from './utils/isFunction';\n\n// Methods\nimport update from './methods/update';\nimport destroy from './methods/destroy';\nimport enableEventListeners from './methods/enableEventListeners';\nimport disableEventListeners from './methods/disableEventListeners';\nimport Defaults from './methods/defaults';\nimport placements from './methods/placements';\n\nexport default class Popper {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  constructor(reference, popper, options = {}) {\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = { ...Popper.Defaults, ...options };\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: [],\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys({\n      ...Popper.Defaults.modifiers,\n      ...options.modifiers,\n    }).forEach(name => {\n      this.options.modifiers[name] = {\n        // If it's a built-in modifier, use it as base\n        ...(Popper.Defaults.modifiers[name] || {}),\n        // If there are custom options, override and merge with default ones\n        ...(options.modifiers ? options.modifiers[name] : {}),\n      };\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers)\n      .map(name => ({\n        name,\n        ...this.options.modifiers[name],\n      }))\n      // sort the modifiers by order\n      .sort((a, b) => a.order - b.order);\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(modifierOptions => {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(\n          this.reference,\n          this.popper,\n          this.options,\n          modifierOptions,\n          this.state\n        );\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    const eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n  update() {\n    return update.call(this);\n  }\n  destroy() {\n    return destroy.call(this);\n  }\n  enableEventListeners() {\n    return enableEventListeners.call(this);\n  }\n  disableEventListeners() {\n    return disableEventListeners.call(this);\n  }\n\n  /**\n   * Schedules an update. It will run on the next UI update available.\n   * @method scheduleUpdate\n   * @memberof Popper\n   */\n  scheduleUpdate = () => requestAnimationFrame(this.update);\n\n  /**\n   * Collection of utilities useful when writing custom modifiers.\n   * Starting from version 1.7, this method is available only if you\n   * include `popper-utils.js` before `popper.js`.\n   *\n   * **DEPRECATION**: This way to access PopperUtils is deprecated\n   * and will be removed in v2! Use the PopperUtils module directly instead.\n   * Due to the high instability of the methods contained in Utils, we can't\n   * guarantee them to follow semver. Use them at your own risk!\n   * @static\n   * @private\n   * @type {Object}\n   * @deprecated since version 1.8\n   * @member Utils\n   * @memberof Popper\n   */\n  static Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\n\n  static placements = placements;\n\n  static Defaults = Defaults;\n}\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n"], "names": ["window", "document", "navigator", "timeoutDuration", "longerTimeoutBrowsers", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "userAgent", "indexOf", "microtaskDebounce", "fn", "called", "Promise", "resolve", "then", "taskDebounce", "scheduled", "supportsMicroTasks", "isFunction", "functionToCheck", "getType", "toString", "call", "getStyleComputedProperty", "element", "property", "nodeType", "ownerDocument", "defaultView", "css", "getComputedStyle", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "body", "overflow", "overflowX", "overflowY", "test", "getReferenceNode", "reference", "referenceNode", "isIE11", "MSInputMethodContext", "documentMode", "isIE10", "isIE", "version", "getOffsetParent", "documentElement", "noOffsetParent", "offsetParent", "nextElement<PERSON><PERSON>ling", "isOffsetContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "element1root", "getScroll", "side", "upperSide", "html", "scrollingElement", "includeScroll", "rect", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getBordersSize", "styles", "axis", "sideA", "sideB", "parseFloat", "getSize", "computedStyle", "Math", "max", "parseInt", "getWindowSizes", "getClientRect", "offsets", "width", "height", "getBoundingClientRect", "e", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getOffsetRectRelativeToArbitraryNode", "children", "parent", "fixedPosition", "runIsIE", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "excludeScroll", "relativeOffset", "innerWidth", "innerHeight", "offset", "isFixed", "getFixedPositionOffsetParent", "parentElement", "el", "getBoundaries", "popper", "padding", "boundariesElement", "boundaries", "boundariesNode", "isPaddingNumber", "getArea", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "Object", "keys", "map", "key", "sort", "a", "b", "area", "filtered<PERSON><PERSON>s", "filter", "computedPlacement", "variation", "split", "getReferenceOffsets", "state", "commonOffsetParent", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "find", "arr", "check", "Array", "prototype", "findIndex", "prop", "value", "cur", "match", "obj", "runModifiers", "modifiers", "data", "ends", "modifiersToRun", "undefined", "slice", "for<PERSON>ach", "warn", "enabled", "update", "isDestroyed", "options", "positionFixed", "flip", "originalPlacement", "position", "isCreated", "onCreate", "onUpdate", "isModifierEnabled", "modifierName", "some", "name", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "prefix", "to<PERSON><PERSON><PERSON>", "style", "destroy", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "disableEventListeners", "removeOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "getWindow", "attachToScrollParents", "event", "callback", "scrollParents", "isBody", "target", "addEventListener", "passive", "push", "setupEventListeners", "updateBound", "scrollElement", "eventsEnabled", "enableEventListeners", "scheduleUpdate", "removeEventListeners", "removeEventListener", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "setAttributes", "attributes", "setAttribute", "applyStyle", "instance", "arrowElement", "arrowStyles", "applyStyleOnLoad", "modifierOptions", "getRoundedOffsets", "shouldRound", "round", "floor", "noRound", "v", "referenceWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isVertical", "isVariation", "sameWidthParity", "bothOddWidth", "horizontalToInteger", "verticalToInteger", "isFirefox", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "devicePixelRatio", "prefixedProperty", "invertTop", "invertLeft", "arrow", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "requested", "querySelector", "len", "sideCapitalized", "toLowerCase", "altSide", "opSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "min", "getOppositeVariation", "validPlacements", "placements", "clockwise", "counter", "index", "concat", "reverse", "BEHAVIORS", "flipped", "placementOpposite", "flipOrder", "behavior", "FLIP", "CLOCKWISE", "COUNTERCLOCKWISE", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariationByRef", "flipVariations", "flippedVariationByContent", "flipVariationsByContent", "flippedVariation", "keepTogether", "toValue", "str", "size", "parseOffset", "basePlacement", "useHeight", "fragments", "frag", "trim", "divider", "search", "splitRegex", "ops", "op", "mergeWithPrevious", "reduce", "index2", "preventOverflow", "transformProp", "popperStyles", "transform", "priority", "escapeWithReference", "shift", "shiftvariation", "shiftOffsets", "hide", "bound", "inner", "subtractLength", "<PERSON><PERSON>", "requestAnimationFrame", "debounce", "bind", "De<PERSON>ults", "j<PERSON>y", "onLoad", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,gBAAe,OAAOA,MAAP,KAAkB,WAAlB,IAAiC,OAAOC,QAAP,KAAoB,WAArD,IAAoE,OAAOC,SAAP,KAAqB,WAAxG;;ACEA,MAAMC,kBAAmB,YAAU;QAC3BC,wBAAwB,CAAC,MAAD,EAAS,SAAT,EAAoB,SAApB,CAA9B;OACK,IAAIC,IAAI,CAAb,EAAgBA,IAAID,sBAAsBE,MAA1C,EAAkDD,KAAK,CAAvD,EAA0D;QACpDE,aAAaL,UAAUM,SAAV,CAAoBC,OAApB,CAA4BL,sBAAsBC,CAAtB,CAA5B,KAAyD,CAA1E,EAA6E;aACpE,CAAP;;;SAGG,CAAP;CAPuB,EAAzB;;AAUA,AAAO,SAASK,iBAAT,CAA2BC,EAA3B,EAA+B;MAChCC,SAAS,KAAb;SACO,MAAM;QACPA,MAAJ,EAAY;;;aAGH,IAAT;WACOC,OAAP,CAAeC,OAAf,GAAyBC,IAAzB,CAA8B,MAAM;eACzB,KAAT;;KADF;GALF;;;AAYF,AAAO,SAASC,YAAT,CAAsBL,EAAtB,EAA0B;MAC3BM,YAAY,KAAhB;SACO,MAAM;QACP,CAACA,SAAL,EAAgB;kBACF,IAAZ;iBACW,MAAM;oBACH,KAAZ;;OADF,EAGGd,eAHH;;GAHJ;;;AAWF,MAAMe,qBAAqBX,aAAaP,OAAOa,OAA/C;;;;;;;;;;;AAYA,eAAgBK,qBACZR,iBADY,GAEZM,YAFJ;;ACnDA;;;;;;;AAOA,AAAe,SAASG,UAAT,CAAoBC,eAApB,EAAqC;QAC5CC,UAAU,EAAhB;SAEED,mBACAC,QAAQC,QAAR,CAAiBC,IAAjB,CAAsBH,eAAtB,MAA2C,mBAF7C;;;ACTF;;;;;;;AAOA,AAAe,SAASI,wBAAT,CAAkCC,OAAlC,EAA2CC,QAA3C,EAAqD;MAC9DD,QAAQE,QAAR,KAAqB,CAAzB,EAA4B;WACnB,EAAP;;;QAGI3B,SAASyB,QAAQG,aAAR,CAAsBC,WAArC;QACMC,MAAM9B,OAAO+B,gBAAP,CAAwBN,OAAxB,EAAiC,IAAjC,CAAZ;SACOC,WAAWI,IAAIJ,QAAJ,CAAX,GAA2BI,GAAlC;;;ACdF;;;;;;;AAOA,AAAe,SAASE,aAAT,CAAuBP,OAAvB,EAAgC;MACzCA,QAAQQ,QAAR,KAAqB,MAAzB,EAAiC;WACxBR,OAAP;;SAEKA,QAAQS,UAAR,IAAsBT,QAAQU,IAArC;;;ACRF;;;;;;;AAOA,AAAe,SAASC,eAAT,CAAyBX,OAAzB,EAAkC;;MAE3C,CAACA,OAAL,EAAc;WACLxB,SAASoC,IAAhB;;;UAGMZ,QAAQQ,QAAhB;SACO,MAAL;SACK,MAAL;aACSR,QAAQG,aAAR,CAAsBS,IAA7B;SACG,WAAL;aACSZ,QAAQY,IAAf;;;;QAIE,EAAEC,QAAF,EAAYC,SAAZ,EAAuBC,SAAvB,KAAqChB,yBAAyBC,OAAzB,CAA3C;MACI,wBAAwBgB,IAAxB,CAA6BH,WAAWE,SAAX,GAAuBD,SAApD,CAAJ,EAAoE;WAC3Dd,OAAP;;;SAGKW,gBAAgBJ,cAAcP,OAAd,CAAhB,CAAP;;;AC9BF;;;;;;;AAOA,AAAe,SAASiB,gBAAT,CAA0BC,SAA1B,EAAqC;SAC3CA,aAAaA,UAAUC,aAAvB,GAAuCD,UAAUC,aAAjD,GAAiED,SAAxE;;;ACNF,MAAME,SAAStC,aAAa,CAAC,EAAEP,OAAO8C,oBAAP,IAA+B7C,SAAS8C,YAA1C,CAA7B;AACA,MAAMC,SAASzC,aAAa,UAAUkC,IAAV,CAAevC,UAAUM,SAAzB,CAA5B;;;;;;;;;AASA,AAAe,SAASyC,IAAT,CAAcC,OAAd,EAAuB;MAChCA,YAAY,EAAhB,EAAoB;WACXL,MAAP;;MAEEK,YAAY,EAAhB,EAAoB;WACXF,MAAP;;SAEKH,UAAUG,MAAjB;;;ACjBF;;;;;;;AAOA,AAAe,SAASG,eAAT,CAAyB1B,OAAzB,EAAkC;MAC3C,CAACA,OAAL,EAAc;WACLxB,SAASmD,eAAhB;;;QAGIC,iBAAiBJ,KAAK,EAAL,IAAWhD,SAASoC,IAApB,GAA2B,IAAlD;;;MAGIiB,eAAe7B,QAAQ6B,YAAR,IAAwB,IAA3C;;SAEOA,iBAAiBD,cAAjB,IAAmC5B,QAAQ8B,kBAAlD,EAAsE;mBACrD,CAAC9B,UAAUA,QAAQ8B,kBAAnB,EAAuCD,YAAtD;;;QAGIrB,WAAWqB,gBAAgBA,aAAarB,QAA9C;;MAEI,CAACA,QAAD,IAAaA,aAAa,MAA1B,IAAoCA,aAAa,MAArD,EAA6D;WACpDR,UAAUA,QAAQG,aAAR,CAAsBwB,eAAhC,GAAkDnD,SAASmD,eAAlE;;;;;MAMA,CAAC,IAAD,EAAO,IAAP,EAAa,OAAb,EAAsB3C,OAAtB,CAA8B6C,aAAarB,QAA3C,MAAyD,CAAC,CAA1D,IACAT,yBAAyB8B,YAAzB,EAAuC,UAAvC,MAAuD,QAFzD,EAGE;WACOH,gBAAgBG,YAAhB,CAAP;;;SAGKA,YAAP;;;ACpCa,SAASE,iBAAT,CAA2B/B,OAA3B,EAAoC;QAC3C,EAAEQ,QAAF,KAAeR,OAArB;MACIQ,aAAa,MAAjB,EAAyB;WAChB,KAAP;;SAGAA,aAAa,MAAb,IAAuBkB,gBAAgB1B,QAAQgC,iBAAxB,MAA+ChC,OADxE;;;ACPF;;;;;;;AAOA,AAAe,SAASiC,OAAT,CAAiBC,IAAjB,EAAuB;MAChCA,KAAKzB,UAAL,KAAoB,IAAxB,EAA8B;WACrBwB,QAAQC,KAAKzB,UAAb,CAAP;;;SAGKyB,IAAP;;;ACRF;;;;;;;;AAQA,AAAe,SAASC,sBAAT,CAAgCC,QAAhC,EAA0CC,QAA1C,EAAoD;;MAE7D,CAACD,QAAD,IAAa,CAACA,SAASlC,QAAvB,IAAmC,CAACmC,QAApC,IAAgD,CAACA,SAASnC,QAA9D,EAAwE;WAC/D1B,SAASmD,eAAhB;;;;QAIIW,QACJF,SAASG,uBAAT,CAAiCF,QAAjC,IACAG,KAAKC,2BAFP;QAGMC,QAAQJ,QAAQF,QAAR,GAAmBC,QAAjC;QACMM,MAAML,QAAQD,QAAR,GAAmBD,QAA/B;;;QAGMQ,QAAQpE,SAASqE,WAAT,EAAd;QACMC,QAAN,CAAeJ,KAAf,EAAsB,CAAtB;QACMK,MAAN,CAAaJ,GAAb,EAAkB,CAAlB;QACM,EAAEK,uBAAF,KAA8BJ,KAApC;;;MAIGR,aAAaY,uBAAb,IACCX,aAAaW,uBADf,IAEAN,MAAMO,QAAN,CAAeN,GAAf,CAHF,EAIE;QACIZ,kBAAkBiB,uBAAlB,CAAJ,EAAgD;aACvCA,uBAAP;;;WAGKtB,gBAAgBsB,uBAAhB,CAAP;;;;QAIIE,eAAejB,QAAQG,QAAR,CAArB;MACIc,aAAaxC,IAAjB,EAAuB;WACdyB,uBAAuBe,aAAaxC,IAApC,EAA0C2B,QAA1C,CAAP;GADF,MAEO;WACEF,uBAAuBC,QAAvB,EAAiCH,QAAQI,QAAR,EAAkB3B,IAAnD,CAAP;;;;ACjDJ;;;;;;;;AAQA,AAAe,SAASyC,SAAT,CAAmBnD,OAAnB,EAA4BoD,OAAO,KAAnC,EAA0C;QACjDC,YAAYD,SAAS,KAAT,GAAiB,WAAjB,GAA+B,YAAjD;QACM5C,WAAWR,QAAQQ,QAAzB;;MAEIA,aAAa,MAAb,IAAuBA,aAAa,MAAxC,EAAgD;UACxC8C,OAAOtD,QAAQG,aAAR,CAAsBwB,eAAnC;UACM4B,mBAAmBvD,QAAQG,aAAR,CAAsBoD,gBAAtB,IAA0CD,IAAnE;WACOC,iBAAiBF,SAAjB,CAAP;;;SAGKrD,QAAQqD,SAAR,CAAP;;;AChBF;;;;;;;;;AASA,AAAe,SAASG,aAAT,CAAuBC,IAAvB,EAA6BzD,OAA7B,EAAsC0D,WAAW,KAAjD,EAAwD;QAC/DC,YAAYR,UAAUnD,OAAV,EAAmB,KAAnB,CAAlB;QACM4D,aAAaT,UAAUnD,OAAV,EAAmB,MAAnB,CAAnB;QACM6D,WAAWH,WAAW,CAAC,CAAZ,GAAgB,CAAjC;OACKI,GAAL,IAAYH,YAAYE,QAAxB;OACKE,MAAL,IAAeJ,YAAYE,QAA3B;OACKG,IAAL,IAAaJ,aAAaC,QAA1B;OACKI,KAAL,IAAcL,aAAaC,QAA3B;SACOJ,IAAP;;;ACnBF;;;;;;;;;;AAUA,AAAe,SAASS,cAAT,CAAwBC,MAAxB,EAAgCC,IAAhC,EAAsC;QAC7CC,QAAQD,SAAS,GAAT,GAAe,MAAf,GAAwB,KAAtC;QACME,QAAQD,UAAU,MAAV,GAAmB,OAAnB,GAA6B,QAA3C;;SAGEE,WAAWJ,OAAQ,SAAQE,KAAM,OAAtB,CAAX,IACAE,WAAWJ,OAAQ,SAAQG,KAAM,OAAtB,CAAX,CAFF;;;ACZF,SAASE,OAAT,CAAiBJ,IAAjB,EAAuBxD,IAAvB,EAA6B0C,IAA7B,EAAmCmB,aAAnC,EAAkD;SACzCC,KAAKC,GAAL,CACL/D,KAAM,SAAQwD,IAAK,EAAnB,CADK,EAELxD,KAAM,SAAQwD,IAAK,EAAnB,CAFK,EAGLd,KAAM,SAAQc,IAAK,EAAnB,CAHK,EAILd,KAAM,SAAQc,IAAK,EAAnB,CAJK,EAKLd,KAAM,SAAQc,IAAK,EAAnB,CALK,EAML5C,KAAK,EAAL,IACKoD,SAAStB,KAAM,SAAQc,IAAK,EAAnB,CAAT,IACHQ,SAASH,cAAe,SAAQL,SAAS,QAAT,GAAoB,KAApB,GAA4B,MAAO,EAA1D,CAAT,CADG,GAEHQ,SAASH,cAAe,SAAQL,SAAS,QAAT,GAAoB,QAApB,GAA+B,OAAQ,EAA9D,CAAT,CAHF,GAIE,CAVG,CAAP;;;AAcF,AAAe,SAASS,cAAT,CAAwBrG,QAAxB,EAAkC;QACzCoC,OAAOpC,SAASoC,IAAtB;QACM0C,OAAO9E,SAASmD,eAAtB;QACM8C,gBAAgBjD,KAAK,EAAL,KAAYlB,iBAAiBgD,IAAjB,CAAlC;;SAEO;YACGkB,QAAQ,QAAR,EAAkB5D,IAAlB,EAAwB0C,IAAxB,EAA8BmB,aAA9B,CADH;WAEED,QAAQ,OAAR,EAAiB5D,IAAjB,EAAuB0C,IAAvB,EAA6BmB,aAA7B;GAFT;;;;;;;;;;;;;;;;;ACtBF;;;;;;;AAOA,AAAe,SAASK,aAAT,CAAuBC,OAAvB,EAAgC;sBAExCA,OADL;WAESA,QAAQf,IAAR,GAAee,QAAQC,KAFhC;YAGUD,QAAQjB,GAAR,GAAciB,QAAQE;;;;ACJlC;;;;;;;AAOA,AAAe,SAASC,qBAAT,CAA+BlF,OAA/B,EAAwC;MACjDyD,OAAO,EAAX;;;;;MAKI;QACEjC,KAAK,EAAL,CAAJ,EAAc;aACLxB,QAAQkF,qBAAR,EAAP;YACMvB,YAAYR,UAAUnD,OAAV,EAAmB,KAAnB,CAAlB;YACM4D,aAAaT,UAAUnD,OAAV,EAAmB,MAAnB,CAAnB;WACK8D,GAAL,IAAYH,SAAZ;WACKK,IAAL,IAAaJ,UAAb;WACKG,MAAL,IAAeJ,SAAf;WACKM,KAAL,IAAcL,UAAd;KAPF,MASK;aACI5D,QAAQkF,qBAAR,EAAP;;GAXJ,CAcA,OAAMC,CAAN,EAAQ;;QAEFC,SAAS;UACP3B,KAAKO,IADE;SAERP,KAAKK,GAFG;WAGNL,KAAKQ,KAAL,GAAaR,KAAKO,IAHZ;YAILP,KAAKM,MAAL,GAAcN,KAAKK;GAJ7B;;;QAQMuB,QAAQrF,QAAQQ,QAAR,KAAqB,MAArB,GAA8BqE,eAAe7E,QAAQG,aAAvB,CAA9B,GAAsE,EAApF;QACM6E,QACJK,MAAML,KAAN,IAAehF,QAAQsF,WAAvB,IAAsCF,OAAOJ,KAD/C;QAEMC,SACJI,MAAMJ,MAAN,IAAgBjF,QAAQuF,YAAxB,IAAwCH,OAAOH,MADjD;;MAGIO,iBAAiBxF,QAAQyF,WAAR,GAAsBT,KAA3C;MACIU,gBAAgB1F,QAAQ2F,YAAR,GAAuBV,MAA3C;;;;MAIIO,kBAAkBE,aAAtB,EAAqC;UAC7BvB,SAASpE,yBAAyBC,OAAzB,CAAf;sBACkBkE,eAAeC,MAAf,EAAuB,GAAvB,CAAlB;qBACiBD,eAAeC,MAAf,EAAuB,GAAvB,CAAjB;;WAEOa,KAAP,IAAgBQ,cAAhB;WACOP,MAAP,IAAiBS,aAAjB;;;SAGKZ,cAAcM,MAAd,CAAP;;;ACzDa,SAASQ,oCAAT,CAA8CC,QAA9C,EAAwDC,MAAxD,EAAgEC,gBAAgB,KAAhF,EAAuF;QAC9FxE,SAASyE,KAAQ,EAAR,CAAf;QACMC,SAASH,OAAOtF,QAAP,KAAoB,MAAnC;QACM0F,eAAehB,sBAAsBW,QAAtB,CAArB;QACMM,aAAajB,sBAAsBY,MAAtB,CAAnB;QACMM,eAAezF,gBAAgBkF,QAAhB,CAArB;;QAEM1B,SAASpE,yBAAyB+F,MAAzB,CAAf;QACMO,iBAAiB9B,WAAWJ,OAAOkC,cAAlB,CAAvB;QACMC,kBAAkB/B,WAAWJ,OAAOmC,eAAlB,CAAxB;;;MAGGP,iBAAiBE,MAApB,EAA4B;eACfnC,GAAX,GAAiBY,KAAKC,GAAL,CAASwB,WAAWrC,GAApB,EAAyB,CAAzB,CAAjB;eACWE,IAAX,GAAkBU,KAAKC,GAAL,CAASwB,WAAWnC,IAApB,EAA0B,CAA1B,CAAlB;;MAEEe,UAAUD,cAAc;SACrBoB,aAAapC,GAAb,GAAmBqC,WAAWrC,GAA9B,GAAoCuC,cADf;UAEpBH,aAAalC,IAAb,GAAoBmC,WAAWnC,IAA/B,GAAsCsC,eAFlB;WAGnBJ,aAAalB,KAHM;YAIlBkB,aAAajB;GAJT,CAAd;UAMQsB,SAAR,GAAoB,CAApB;UACQC,UAAR,GAAqB,CAArB;;;;;;MAMI,CAACjF,MAAD,IAAW0E,MAAf,EAAuB;UACfM,YAAYhC,WAAWJ,OAAOoC,SAAlB,CAAlB;UACMC,aAAajC,WAAWJ,OAAOqC,UAAlB,CAAnB;;YAEQ1C,GAAR,IAAeuC,iBAAiBE,SAAhC;YACQxC,MAAR,IAAkBsC,iBAAiBE,SAAnC;YACQvC,IAAR,IAAgBsC,kBAAkBE,UAAlC;YACQvC,KAAR,IAAiBqC,kBAAkBE,UAAnC;;;YAGQD,SAAR,GAAoBA,SAApB;YACQC,UAAR,GAAqBA,UAArB;;;MAIAjF,UAAU,CAACwE,aAAX,GACID,OAAO7C,QAAP,CAAgBmD,YAAhB,CADJ,GAEIN,WAAWM,YAAX,IAA2BA,aAAa5F,QAAb,KAA0B,MAH3D,EAIE;cACUgD,cAAcuB,OAAd,EAAuBe,MAAvB,CAAV;;;SAGKf,OAAP;;;ACtDa,SAAS0B,6CAAT,CAAuDzG,OAAvD,EAAgE0G,gBAAgB,KAAhF,EAAuF;QAC9FpD,OAAOtD,QAAQG,aAAR,CAAsBwB,eAAnC;QACMgF,iBAAiBf,qCAAqC5F,OAArC,EAA8CsD,IAA9C,CAAvB;QACM0B,QAAQN,KAAKC,GAAL,CAASrB,KAAKgC,WAAd,EAA2B/G,OAAOqI,UAAP,IAAqB,CAAhD,CAAd;QACM3B,SAASP,KAAKC,GAAL,CAASrB,KAAKiC,YAAd,EAA4BhH,OAAOsI,WAAP,IAAsB,CAAlD,CAAf;;QAEMlD,YAAY,CAAC+C,aAAD,GAAiBvD,UAAUG,IAAV,CAAjB,GAAmC,CAArD;QACMM,aAAa,CAAC8C,aAAD,GAAiBvD,UAAUG,IAAV,EAAgB,MAAhB,CAAjB,GAA2C,CAA9D;;QAEMwD,SAAS;SACRnD,YAAYgD,eAAe7C,GAA3B,GAAiC6C,eAAeJ,SADxC;UAEP3C,aAAa+C,eAAe3C,IAA5B,GAAmC2C,eAAeH,UAF3C;SAAA;;GAAf;;SAOO1B,cAAcgC,MAAd,CAAP;;;ACjBF;;;;;;;;AAQA,AAAe,SAASC,OAAT,CAAiB/G,OAAjB,EAA0B;QACjCQ,WAAWR,QAAQQ,QAAzB;MACIA,aAAa,MAAb,IAAuBA,aAAa,MAAxC,EAAgD;WACvC,KAAP;;MAEET,yBAAyBC,OAAzB,EAAkC,UAAlC,MAAkD,OAAtD,EAA+D;WACtD,IAAP;;QAEIS,aAAaF,cAAcP,OAAd,CAAnB;MACI,CAACS,UAAL,EAAiB;WACR,KAAP;;SAEKsG,QAAQtG,UAAR,CAAP;;;ACrBF;;;;;;;;AAQA,AAAe,SAASuG,4BAAT,CAAsChH,OAAtC,EAA+C;;MAEvD,CAACA,OAAD,IAAY,CAACA,QAAQiH,aAArB,IAAsCzF,MAA1C,EAAkD;WAC1ChD,SAASmD,eAAhB;;MAEEuF,KAAKlH,QAAQiH,aAAjB;SACOC,MAAMnH,yBAAyBmH,EAAzB,EAA6B,WAA7B,MAA8C,MAA3D,EAAmE;SAC5DA,GAAGD,aAAR;;SAEKC,MAAM1I,SAASmD,eAAtB;;;ACTF;;;;;;;;;;;AAWA,AAAe,SAASwF,aAAT,CACbC,MADa,EAEblG,SAFa,EAGbmG,OAHa,EAIbC,iBAJa,EAKbvB,gBAAgB,KALH,EAMb;;;MAGIwB,aAAa,EAAEzD,KAAK,CAAP,EAAUE,MAAM,CAAhB,EAAjB;QACMnC,eAAekE,gBAAgBiB,6BAA6BI,MAA7B,CAAhB,GAAuDjF,uBAAuBiF,MAAvB,EAA+BnG,iBAAiBC,SAAjB,CAA/B,CAA5E;;;MAGIoG,sBAAsB,UAA1B,EAAuC;iBACxBb,8CAA8C5E,YAA9C,EAA4DkE,aAA5D,CAAb;GADF,MAIK;;QAECyB,cAAJ;QACIF,sBAAsB,cAA1B,EAA0C;uBACvB3G,gBAAgBJ,cAAcW,SAAd,CAAhB,CAAjB;UACIsG,eAAehH,QAAf,KAA4B,MAAhC,EAAwC;yBACrB4G,OAAOjH,aAAP,CAAqBwB,eAAtC;;KAHJ,MAKO,IAAI2F,sBAAsB,QAA1B,EAAoC;uBACxBF,OAAOjH,aAAP,CAAqBwB,eAAtC;KADK,MAEA;uBACY2F,iBAAjB;;;UAGIvC,UAAUa,qCACd4B,cADc,EAEd3F,YAFc,EAGdkE,aAHc,CAAhB;;;QAOIyB,eAAehH,QAAf,KAA4B,MAA5B,IAAsC,CAACuG,QAAQlF,YAAR,CAA3C,EAAkE;YAC1D,EAAEoD,MAAF,EAAUD,KAAV,KAAoBH,eAAeuC,OAAOjH,aAAtB,CAA1B;iBACW2D,GAAX,IAAkBiB,QAAQjB,GAAR,GAAciB,QAAQwB,SAAxC;iBACWxC,MAAX,GAAoBkB,SAASF,QAAQjB,GAArC;iBACWE,IAAX,IAAmBe,QAAQf,IAAR,GAAee,QAAQyB,UAA1C;iBACWvC,KAAX,GAAmBe,QAAQD,QAAQf,IAAnC;KALF,MAMO;;mBAEQe,OAAb;;;;;YAKMsC,WAAW,CAArB;QACMI,kBAAkB,OAAOJ,OAAP,KAAmB,QAA3C;aACWrD,IAAX,IAAmByD,kBAAkBJ,OAAlB,GAA4BA,QAAQrD,IAAR,IAAgB,CAA/D;aACWF,GAAX,IAAkB2D,kBAAkBJ,OAAlB,GAA4BA,QAAQvD,GAAR,IAAe,CAA7D;aACWG,KAAX,IAAoBwD,kBAAkBJ,OAAlB,GAA4BA,QAAQpD,KAAR,IAAiB,CAAjE;aACWF,MAAX,IAAqB0D,kBAAkBJ,OAAlB,GAA4BA,QAAQtD,MAAR,IAAkB,CAAnE;;SAEOwD,UAAP;;;AC7EF,SAASG,OAAT,CAAiB,EAAE1C,KAAF,EAASC,MAAT,EAAjB,EAAoC;SAC3BD,QAAQC,MAAf;;;;;;;;;;;;AAYF,AAAe,SAAS0C,oBAAT,CACbC,SADa,EAEbC,OAFa,EAGbT,MAHa,EAIblG,SAJa,EAKboG,iBALa,EAMbD,UAAU,CANG,EAOb;MACIO,UAAU5I,OAAV,CAAkB,MAAlB,MAA8B,CAAC,CAAnC,EAAsC;WAC7B4I,SAAP;;;QAGIL,aAAaJ,cACjBC,MADiB,EAEjBlG,SAFiB,EAGjBmG,OAHiB,EAIjBC,iBAJiB,CAAnB;;QAOMQ,QAAQ;SACP;aACIP,WAAWvC,KADf;cAEK6C,QAAQ/D,GAAR,GAAcyD,WAAWzD;KAHvB;WAKL;aACEyD,WAAWtD,KAAX,GAAmB4D,QAAQ5D,KAD7B;cAEGsD,WAAWtC;KAPT;YASJ;aACCsC,WAAWvC,KADZ;cAEEuC,WAAWxD,MAAX,GAAoB8D,QAAQ9D;KAX1B;UAaN;aACG8D,QAAQ7D,IAAR,GAAeuD,WAAWvD,IAD7B;cAEIuD,WAAWtC;;GAfvB;;QAmBM8C,cAAcC,OAAOC,IAAP,CAAYH,KAAZ,EACjBI,GADiB,CACbC;;KAEAL,MAAMK,GAAN,CAFA;UAGGT,QAAQI,MAAMK,GAAN,CAAR;IAJU,EAMjBC,IANiB,CAMZ,CAACC,CAAD,EAAIC,CAAJ,KAAUA,EAAEC,IAAF,GAASF,EAAEE,IANT,CAApB;;QAQMC,gBAAgBT,YAAYU,MAAZ,CACpB,CAAC,EAAEzD,KAAF,EAASC,MAAT,EAAD,KACED,SAASoC,OAAO9B,WAAhB,IAA+BL,UAAUmC,OAAO7B,YAF9B,CAAtB;;QAKMmD,oBAAoBF,cAAc3J,MAAd,GAAuB,CAAvB,GACtB2J,cAAc,CAAd,EAAiBL,GADK,GAEtBJ,YAAY,CAAZ,EAAeI,GAFnB;;QAIMQ,YAAYf,UAAUgB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAlB;;SAEOF,qBAAqBC,YAAa,IAAGA,SAAU,EAA1B,GAA8B,EAAnD,CAAP;;;ACnEF;;;;;;;;;;AAUA,AAAe,SAASE,mBAAT,CAA6BC,KAA7B,EAAoC1B,MAApC,EAA4ClG,SAA5C,EAAuD6E,gBAAgB,IAAvE,EAA6E;QACpFgD,qBAAqBhD,gBAAgBiB,6BAA6BI,MAA7B,CAAhB,GAAuDjF,uBAAuBiF,MAAvB,EAA+BnG,iBAAiBC,SAAjB,CAA/B,CAAlF;SACO0E,qCAAqC1E,SAArC,EAAgD6H,kBAAhD,EAAoEhD,aAApE,CAAP;;;ACjBF;;;;;;;AAOA,AAAe,SAASiD,aAAT,CAAuBhJ,OAAvB,EAAgC;QACvCzB,SAASyB,QAAQG,aAAR,CAAsBC,WAArC;QACM+D,SAAS5F,OAAO+B,gBAAP,CAAwBN,OAAxB,CAAf;QACMiJ,IAAI1E,WAAWJ,OAAOoC,SAAP,IAAoB,CAA/B,IAAoChC,WAAWJ,OAAO+E,YAAP,IAAuB,CAAlC,CAA9C;QACMC,IAAI5E,WAAWJ,OAAOqC,UAAP,IAAqB,CAAhC,IAAqCjC,WAAWJ,OAAOiF,WAAP,IAAsB,CAAjC,CAA/C;QACMhE,SAAS;WACNpF,QAAQyF,WAAR,GAAsB0D,CADhB;YAELnJ,QAAQ2F,YAAR,GAAuBsD;GAFjC;SAIO7D,MAAP;;;AChBF;;;;;;;AAOA,AAAe,SAASiE,oBAAT,CAA8BzB,SAA9B,EAAyC;QAChD0B,OAAO,EAAEtF,MAAM,OAAR,EAAiBC,OAAO,MAAxB,EAAgCF,QAAQ,KAAxC,EAA+CD,KAAK,QAApD,EAAb;SACO8D,UAAU2B,OAAV,CAAkB,wBAAlB,EAA4CC,WAAWF,KAAKE,OAAL,CAAvD,CAAP;;;ACNF;;;;;;;;;;AAUA,AAAe,SAASC,gBAAT,CAA0BrC,MAA1B,EAAkCsC,gBAAlC,EAAoD9B,SAApD,EAA+D;cAChEA,UAAUgB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAZ;;;QAGMe,aAAaX,cAAc5B,MAAd,CAAnB;;;QAGMwC,gBAAgB;WACbD,WAAW3E,KADE;YAEZ2E,WAAW1E;GAFrB;;;QAMM4E,UAAU,CAAC,OAAD,EAAU,MAAV,EAAkB7K,OAAlB,CAA0B4I,SAA1B,MAAyC,CAAC,CAA1D;QACMkC,WAAWD,UAAU,KAAV,GAAkB,MAAnC;QACME,gBAAgBF,UAAU,MAAV,GAAmB,KAAzC;QACMG,cAAcH,UAAU,QAAV,GAAqB,OAAzC;QACMI,uBAAuB,CAACJ,OAAD,GAAW,QAAX,GAAsB,OAAnD;;gBAEcC,QAAd,IACEJ,iBAAiBI,QAAjB,IACAJ,iBAAiBM,WAAjB,IAAgC,CADhC,GAEAL,WAAWK,WAAX,IAA0B,CAH5B;MAIIpC,cAAcmC,aAAlB,EAAiC;kBACjBA,aAAd,IACEL,iBAAiBK,aAAjB,IAAkCJ,WAAWM,oBAAX,CADpC;GADF,MAGO;kBACSF,aAAd,IACEL,iBAAiBL,qBAAqBU,aAArB,CAAjB,CADF;;;SAIKH,aAAP;;;AC5CF;;;;;;;;;AASA,AAAe,SAASM,IAAT,CAAcC,GAAd,EAAmBC,KAAnB,EAA0B;;MAEnCC,MAAMC,SAAN,CAAgBJ,IAApB,EAA0B;WACjBC,IAAID,IAAJ,CAASE,KAAT,CAAP;;;;SAIKD,IAAI1B,MAAJ,CAAW2B,KAAX,EAAkB,CAAlB,CAAP;;;ACdF;;;;;;;;;AASA,AAAe,SAASG,SAAT,CAAmBJ,GAAnB,EAAwBK,IAAxB,EAA8BC,KAA9B,EAAqC;;MAE9CJ,MAAMC,SAAN,CAAgBC,SAApB,EAA+B;WACtBJ,IAAII,SAAJ,CAAcG,OAAOA,IAAIF,IAAJ,MAAcC,KAAnC,CAAP;;;;QAIIE,QAAQT,KAAKC,GAAL,EAAUS,OAAOA,IAAIJ,IAAJ,MAAcC,KAA/B,CAAd;SACON,IAAInL,OAAJ,CAAY2L,KAAZ,CAAP;;;ACfF;;;;;;;;;;AAUA,AAAe,SAASE,YAAT,CAAsBC,SAAtB,EAAiCC,IAAjC,EAAuCC,IAAvC,EAA6C;QACpDC,iBAAiBD,SAASE,SAAT,GACnBJ,SADmB,GAEnBA,UAAUK,KAAV,CAAgB,CAAhB,EAAmBZ,UAAUO,SAAV,EAAqB,MAArB,EAA6BE,IAA7B,CAAnB,CAFJ;;iBAIeI,OAAf,CAAuBvH,YAAY;QAC7BA,SAAS,UAAT,CAAJ,EAA0B;;cAChBwH,IAAR,CAAa,uDAAb;;UAEInM,KAAK2E,SAAS,UAAT,KAAwBA,SAAS3E,EAA5C,CAJiC;QAK7B2E,SAASyH,OAAT,IAAoB5L,WAAWR,EAAX,CAAxB,EAAwC;;;;WAIjC6F,OAAL,CAAaqC,MAAb,GAAsBtC,cAAciG,KAAKhG,OAAL,CAAaqC,MAA3B,CAAtB;WACKrC,OAAL,CAAa7D,SAAb,GAAyB4D,cAAciG,KAAKhG,OAAL,CAAa7D,SAA3B,CAAzB;;aAEOhC,GAAG6L,IAAH,EAASlH,QAAT,CAAP;;GAZJ;;SAgBOkH,IAAP;;;AC9BF;;;;;;;AAOA,AAAe,SAASQ,MAAT,GAAkB;;MAE3B,KAAKzC,KAAL,CAAW0C,WAAf,EAA4B;;;;MAIxBT,OAAO;cACC,IADD;YAED,EAFC;iBAGI,EAHJ;gBAIG,EAJH;aAKA,KALA;aAMA;GANX;;;OAUKhG,OAAL,CAAa7D,SAAb,GAAyB2H,oBACvB,KAAKC,KADkB,EAEvB,KAAK1B,MAFkB,EAGvB,KAAKlG,SAHkB,EAIvB,KAAKuK,OAAL,CAAaC,aAJU,CAAzB;;;;;OAUK9D,SAAL,GAAiBD,qBACf,KAAK8D,OAAL,CAAa7D,SADE,EAEfmD,KAAKhG,OAAL,CAAa7D,SAFE,EAGf,KAAKkG,MAHU,EAIf,KAAKlG,SAJU,EAKf,KAAKuK,OAAL,CAAaX,SAAb,CAAuBa,IAAvB,CAA4BrE,iBALb,EAMf,KAAKmE,OAAL,CAAaX,SAAb,CAAuBa,IAAvB,CAA4BtE,OANb,CAAjB;;;OAUKuE,iBAAL,GAAyBb,KAAKnD,SAA9B;;OAEK8D,aAAL,GAAqB,KAAKD,OAAL,CAAaC,aAAlC;;;OAGK3G,OAAL,CAAaqC,MAAb,GAAsBqC,iBACpB,KAAKrC,MADe,EAEpB2D,KAAKhG,OAAL,CAAa7D,SAFO,EAGpB6J,KAAKnD,SAHe,CAAtB;;OAMK7C,OAAL,CAAaqC,MAAb,CAAoByE,QAApB,GAA+B,KAAKJ,OAAL,CAAaC,aAAb,GAC3B,OAD2B,GAE3B,UAFJ;;;SAKOb,aAAa,KAAKC,SAAlB,EAA6BC,IAA7B,CAAP;;;;MAII,CAAC,KAAKjC,KAAL,CAAWgD,SAAhB,EAA2B;SACpBhD,KAAL,CAAWgD,SAAX,GAAuB,IAAvB;SACKL,OAAL,CAAaM,QAAb,CAAsBhB,IAAtB;GAFF,MAGO;SACAU,OAAL,CAAaO,QAAb,CAAsBjB,IAAtB;;;;ACxEJ;;;;;;AAMA,AAAe,SAASkB,iBAAT,CAA2BnB,SAA3B,EAAsCoB,YAAtC,EAAoD;SAC1DpB,UAAUqB,IAAV,CACL,CAAC,EAAEC,IAAF,EAAQd,OAAR,EAAD,KAAuBA,WAAWc,SAASF,YADtC,CAAP;;;ACPF;;;;;;;AAOA,AAAe,SAASG,wBAAT,CAAkCpM,QAAlC,EAA4C;QACnDqM,WAAW,CAAC,KAAD,EAAQ,IAAR,EAAc,QAAd,EAAwB,KAAxB,EAA+B,GAA/B,CAAjB;QACMC,YAAYtM,SAASuM,MAAT,CAAgB,CAAhB,EAAmBC,WAAnB,KAAmCxM,SAASkL,KAAT,CAAe,CAAf,CAArD;;OAEK,IAAIvM,IAAI,CAAb,EAAgBA,IAAI0N,SAASzN,MAA7B,EAAqCD,GAArC,EAA0C;UAClC8N,SAASJ,SAAS1N,CAAT,CAAf;UACM+N,UAAUD,SAAU,GAAEA,MAAO,GAAEH,SAAU,EAA/B,GAAmCtM,QAAnD;QACI,OAAOzB,SAASoC,IAAT,CAAcgM,KAAd,CAAoBD,OAApB,CAAP,KAAwC,WAA5C,EAAyD;aAChDA,OAAP;;;SAGG,IAAP;;;ACfF;;;;;AAKA,AAAe,SAASE,OAAT,GAAmB;OAC3B/D,KAAL,CAAW0C,WAAX,GAAyB,IAAzB;;;MAGIS,kBAAkB,KAAKnB,SAAvB,EAAkC,YAAlC,CAAJ,EAAqD;SAC9C1D,MAAL,CAAY0F,eAAZ,CAA4B,aAA5B;SACK1F,MAAL,CAAYwF,KAAZ,CAAkBf,QAAlB,GAA6B,EAA7B;SACKzE,MAAL,CAAYwF,KAAZ,CAAkB9I,GAAlB,GAAwB,EAAxB;SACKsD,MAAL,CAAYwF,KAAZ,CAAkB5I,IAAlB,GAAyB,EAAzB;SACKoD,MAAL,CAAYwF,KAAZ,CAAkB3I,KAAlB,GAA0B,EAA1B;SACKmD,MAAL,CAAYwF,KAAZ,CAAkB7I,MAAlB,GAA2B,EAA3B;SACKqD,MAAL,CAAYwF,KAAZ,CAAkBG,UAAlB,GAA+B,EAA/B;SACK3F,MAAL,CAAYwF,KAAZ,CAAkBP,yBAAyB,WAAzB,CAAlB,IAA2D,EAA3D;;;OAGGW,qBAAL;;;;MAII,KAAKvB,OAAL,CAAawB,eAAjB,EAAkC;SAC3B7F,MAAL,CAAY3G,UAAZ,CAAuByM,WAAvB,CAAmC,KAAK9F,MAAxC;;SAEK,IAAP;;;AC9BF;;;;;AAKA,AAAe,SAAS+F,SAAT,CAAmBnN,OAAnB,EAA4B;QACnCG,gBAAgBH,QAAQG,aAA9B;SACOA,gBAAgBA,cAAcC,WAA9B,GAA4C7B,MAAnD;;;ACJF,SAAS6O,qBAAT,CAA+BhH,YAA/B,EAA6CiH,KAA7C,EAAoDC,QAApD,EAA8DC,aAA9D,EAA6E;QACrEC,SAASpH,aAAa5F,QAAb,KAA0B,MAAzC;QACMiN,SAASD,SAASpH,aAAajG,aAAb,CAA2BC,WAApC,GAAkDgG,YAAjE;SACOsH,gBAAP,CAAwBL,KAAxB,EAA+BC,QAA/B,EAAyC,EAAEK,SAAS,IAAX,EAAzC;;MAEI,CAACH,MAAL,EAAa;0BAET7M,gBAAgB8M,OAAOhN,UAAvB,CADF,EAEE4M,KAFF,EAGEC,QAHF,EAIEC,aAJF;;gBAOYK,IAAd,CAAmBH,MAAnB;;;;;;;;;AASF,AAAe,SAASI,mBAAT,CACb3M,SADa,EAEbuK,OAFa,EAGb3C,KAHa,EAIbgF,WAJa,EAKb;;QAEMA,WAAN,GAAoBA,WAApB;YACU5M,SAAV,EAAqBwM,gBAArB,CAAsC,QAAtC,EAAgD5E,MAAMgF,WAAtD,EAAmE,EAAEH,SAAS,IAAX,EAAnE;;;QAGMI,gBAAgBpN,gBAAgBO,SAAhB,CAAtB;wBAEE6M,aADF,EAEE,QAFF,EAGEjF,MAAMgF,WAHR,EAIEhF,MAAMyE,aAJR;QAMMQ,aAAN,GAAsBA,aAAtB;QACMC,aAAN,GAAsB,IAAtB;;SAEOlF,KAAP;;;AC5CF;;;;;;AAMA,AAAe,SAASmF,oBAAT,GAAgC;MACzC,CAAC,KAAKnF,KAAL,CAAWkF,aAAhB,EAA+B;SACxBlF,KAAL,GAAa+E,oBACX,KAAK3M,SADM,EAEX,KAAKuK,OAFM,EAGX,KAAK3C,KAHM,EAIX,KAAKoF,cAJM,CAAb;;;;ACRJ;;;;;;AAMA,AAAe,SAASC,oBAAT,CAA8BjN,SAA9B,EAAyC4H,KAAzC,EAAgD;;YAEnD5H,SAAV,EAAqBkN,mBAArB,CAAyC,QAAzC,EAAmDtF,MAAMgF,WAAzD;;;QAGMP,aAAN,CAAoBnC,OAApB,CAA4BqC,UAAU;WAC7BW,mBAAP,CAA2B,QAA3B,EAAqCtF,MAAMgF,WAA3C;GADF;;;QAKMA,WAAN,GAAoB,IAApB;QACMP,aAAN,GAAsB,EAAtB;QACMQ,aAAN,GAAsB,IAAtB;QACMC,aAAN,GAAsB,KAAtB;SACOlF,KAAP;;;ACpBF;;;;;;;AAOA,AAAe,SAASkE,qBAAT,GAAiC;MAC1C,KAAKlE,KAAL,CAAWkF,aAAf,EAA8B;yBACP,KAAKE,cAA1B;SACKpF,KAAL,GAAaqF,qBAAqB,KAAKjN,SAA1B,EAAqC,KAAK4H,KAA1C,CAAb;;;;ACZJ;;;;;;;AAOA,AAAe,SAASuF,SAAT,CAAmBC,CAAnB,EAAsB;SAC5BA,MAAM,EAAN,IAAY,CAACC,MAAMhK,WAAW+J,CAAX,CAAN,CAAb,IAAqCE,SAASF,CAAT,CAA5C;;;ACNF;;;;;;;;AAQA,AAAe,SAASG,SAAT,CAAmBzO,OAAnB,EAA4BmE,MAA5B,EAAoC;SAC1C8D,IAAP,CAAY9D,MAAZ,EAAoBiH,OAApB,CAA4BZ,QAAQ;QAC9BkE,OAAO,EAAX;;QAGE,CAAC,OAAD,EAAU,QAAV,EAAoB,KAApB,EAA2B,OAA3B,EAAoC,QAApC,EAA8C,MAA9C,EAAsD1P,OAAtD,CAA8DwL,IAA9D,MACE,CAAC,CADH,IAEA6D,UAAUlK,OAAOqG,IAAP,CAAV,CAHF,EAIE;aACO,IAAP;;YAEMoC,KAAR,CAAcpC,IAAd,IAAsBrG,OAAOqG,IAAP,IAAekE,IAArC;GAVF;;;ACXF;;;;;;;;AAQA,AAAe,SAASC,aAAT,CAAuB3O,OAAvB,EAAgC4O,UAAhC,EAA4C;SAClD3G,IAAP,CAAY2G,UAAZ,EAAwBxD,OAAxB,CAAgC,UAASZ,IAAT,EAAe;UACvCC,QAAQmE,WAAWpE,IAAX,CAAd;QACIC,UAAU,KAAd,EAAqB;cACXoE,YAAR,CAAqBrE,IAArB,EAA2BoE,WAAWpE,IAAX,CAA3B;KADF,MAEO;cACGsC,eAAR,CAAwBtC,IAAxB;;GALJ;;;ACJF;;;;;;;;;AASA,AAAe,SAASsE,UAAT,CAAoB/D,IAApB,EAA0B;;;;;YAK7BA,KAAKgE,QAAL,CAAc3H,MAAxB,EAAgC2D,KAAK5G,MAArC;;;;gBAIc4G,KAAKgE,QAAL,CAAc3H,MAA5B,EAAoC2D,KAAK6D,UAAzC;;;MAGI7D,KAAKiE,YAAL,IAAqBhH,OAAOC,IAAP,CAAY8C,KAAKkE,WAAjB,EAA8BpQ,MAAvD,EAA+D;cACnDkM,KAAKiE,YAAf,EAA6BjE,KAAKkE,WAAlC;;;SAGKlE,IAAP;;;;;;;;;;;;;AAaF,AAAO,SAASmE,gBAAT,CACLhO,SADK,EAELkG,MAFK,EAGLqE,OAHK,EAIL0D,eAJK,EAKLrG,KALK,EAML;;QAEMY,mBAAmBb,oBAAoBC,KAApB,EAA2B1B,MAA3B,EAAmClG,SAAnC,EAA8CuK,QAAQC,aAAtD,CAAzB;;;;;QAKM9D,YAAYD,qBAChB8D,QAAQ7D,SADQ,EAEhB8B,gBAFgB,EAGhBtC,MAHgB,EAIhBlG,SAJgB,EAKhBuK,QAAQX,SAAR,CAAkBa,IAAlB,CAAuBrE,iBALP,EAMhBmE,QAAQX,SAAR,CAAkBa,IAAlB,CAAuBtE,OANP,CAAlB;;SASOwH,YAAP,CAAoB,aAApB,EAAmCjH,SAAnC;;;;YAIUR,MAAV,EAAkB,EAAEyE,UAAUJ,QAAQC,aAAR,GAAwB,OAAxB,GAAkC,UAA9C,EAAlB;;SAEOD,OAAP;;;ACvEF;;;;;;;;;;;;;;;;;;;AAmBA,AAAe,SAAS2D,iBAAT,CAA2BrE,IAA3B,EAAiCsE,WAAjC,EAA8C;QACrD,EAAEjI,MAAF,EAAUlG,SAAV,KAAwB6J,KAAKhG,OAAnC;QACM,EAAEuK,KAAF,EAASC,KAAT,KAAmB7K,IAAzB;QACM8K,UAAUC,KAAKA,CAArB;;QAEMC,iBAAiBJ,MAAMpO,UAAU8D,KAAhB,CAAvB;QACM2K,cAAcL,MAAMlI,OAAOpC,KAAb,CAApB;;QAEM4K,aAAa,CAAC,MAAD,EAAS,OAAT,EAAkB5Q,OAAlB,CAA0B+L,KAAKnD,SAA/B,MAA8C,CAAC,CAAlE;QACMiI,cAAc9E,KAAKnD,SAAL,CAAe5I,OAAf,CAAuB,GAAvB,MAAgC,CAAC,CAArD;QACM8Q,kBAAkBJ,iBAAiB,CAAjB,KAAuBC,cAAc,CAA7D;QACMI,eAAeL,iBAAiB,CAAjB,KAAuB,CAAvB,IAA4BC,cAAc,CAAd,KAAoB,CAArE;;QAEMK,sBAAsB,CAACX,WAAD,GACxBG,OADwB,GAExBI,cAAcC,WAAd,IAA6BC,eAA7B,GACAR,KADA,GAEAC,KAJJ;QAKMU,oBAAoB,CAACZ,WAAD,GAAeG,OAAf,GAAyBF,KAAnD;;SAEO;UACCU,oBACJD,gBAAgB,CAACF,WAAjB,IAAgCR,WAAhC,GACIjI,OAAOpD,IAAP,GAAc,CADlB,GAEIoD,OAAOpD,IAHP,CADD;SAMAiM,kBAAkB7I,OAAOtD,GAAzB,CANA;YAOGmM,kBAAkB7I,OAAOrD,MAAzB,CAPH;WAQEiM,oBAAoB5I,OAAOnD,KAA3B;GART;;;AChCF,MAAMiM,YAAYpR,aAAa,WAAWkC,IAAX,CAAgBvC,UAAUM,SAA1B,CAA/B;;;;;;;;;AASA,AAAe,SAASoR,YAAT,CAAsBpF,IAAtB,EAA4BU,OAA5B,EAAqC;QAC5C,EAAExC,CAAF,EAAKE,CAAL,KAAWsC,OAAjB;QACM,EAAErE,MAAF,KAAa2D,KAAKhG,OAAxB;;;QAGMqL,8BAA8BlG,KAClCa,KAAKgE,QAAL,CAAcjE,SADoB,EAElCjH,YAAYA,SAASuI,IAAT,KAAkB,YAFI,EAGlCiE,eAHF;MAIID,gCAAgClF,SAApC,EAA+C;YACrCG,IAAR,CACE,+HADF;;QAIIgF,kBACJD,gCAAgClF,SAAhC,GACIkF,2BADJ,GAEI3E,QAAQ4E,eAHd;;QAKMxO,eAAeH,gBAAgBqJ,KAAKgE,QAAL,CAAc3H,MAA9B,CAArB;QACMkJ,mBAAmBpL,sBAAsBrD,YAAtB,CAAzB;;;QAGMsC,SAAS;cACHiD,OAAOyE;GADnB;;QAIM9G,UAAUqK,kBACdrE,IADc,EAEdxM,OAAOgS,gBAAP,GAA0B,CAA1B,IAA+B,CAACL,SAFlB,CAAhB;;QAKM7L,QAAQ4E,MAAM,QAAN,GAAiB,KAAjB,GAAyB,QAAvC;QACM3E,QAAQ6E,MAAM,OAAN,GAAgB,MAAhB,GAAyB,OAAvC;;;;;QAKMqH,mBAAmBnE,yBAAyB,WAAzB,CAAzB;;;;;;;;;;;MAWIrI,IAAJ,EAAUF,GAAV;MACIO,UAAU,QAAd,EAAwB;;;QAGlBxC,aAAarB,QAAb,KAA0B,MAA9B,EAAsC;YAC9B,CAACqB,aAAa0D,YAAd,GAA6BR,QAAQhB,MAA3C;KADF,MAEO;YACC,CAACuM,iBAAiBrL,MAAlB,GAA2BF,QAAQhB,MAAzC;;GANJ,MAQO;UACCgB,QAAQjB,GAAd;;MAEEQ,UAAU,OAAd,EAAuB;QACjBzC,aAAarB,QAAb,KAA0B,MAA9B,EAAsC;aAC7B,CAACqB,aAAayD,WAAd,GAA4BP,QAAQd,KAA3C;KADF,MAEO;aACE,CAACqM,iBAAiBtL,KAAlB,GAA0BD,QAAQd,KAAzC;;GAJJ,MAMO;WACEc,QAAQf,IAAf;;MAEEqM,mBAAmBG,gBAAvB,EAAyC;WAChCA,gBAAP,IAA4B,eAAcxM,IAAK,OAAMF,GAAI,QAAzD;WACOO,KAAP,IAAgB,CAAhB;WACOC,KAAP,IAAgB,CAAhB;WACOyI,UAAP,GAAoB,WAApB;GAJF,MAKO;;UAEC0D,YAAYpM,UAAU,QAAV,GAAqB,CAAC,CAAtB,GAA0B,CAA5C;UACMqM,aAAapM,UAAU,OAAV,GAAoB,CAAC,CAArB,GAAyB,CAA5C;WACOD,KAAP,IAAgBP,MAAM2M,SAAtB;WACOnM,KAAP,IAAgBN,OAAO0M,UAAvB;WACO3D,UAAP,GAAqB,GAAE1I,KAAM,KAAIC,KAAM,EAAvC;;;;QAIIsK,aAAa;mBACF7D,KAAKnD;GADtB;;;OAKKgH,UAAL,gBAAuBA,UAAvB,EAAsC7D,KAAK6D,UAA3C;OACKzK,MAAL,gBAAmBA,MAAnB,EAA8B4G,KAAK5G,MAAnC;OACK8K,WAAL,gBAAwBlE,KAAKhG,OAAL,CAAa4L,KAArC,EAA+C5F,KAAKkE,WAApD;;SAEOlE,IAAP;;;AC5GF;;;;;;;;;;AAUA,AAAe,SAAS6F,kBAAT,CACb9F,SADa,EAEb+F,cAFa,EAGbC,aAHa,EAIb;QACMC,aAAa7G,KAAKY,SAAL,EAAgB,CAAC,EAAEsB,IAAF,EAAD,KAAcA,SAASyE,cAAvC,CAAnB;;QAEMG,aACJ,CAAC,CAACD,UAAF,IACAjG,UAAUqB,IAAV,CAAetI,YAAY;WAEvBA,SAASuI,IAAT,KAAkB0E,aAAlB,IACAjN,SAASyH,OADT,IAEAzH,SAASvB,KAAT,GAAiByO,WAAWzO,KAH9B;GADF,CAFF;;MAUI,CAAC0O,UAAL,EAAiB;UACTD,aAAc,KAAIF,cAAe,IAAvC;UACMI,YAAa,KAAIH,aAAc,IAArC;YACQzF,IAAR,CACG,GAAE4F,SAAU,4BAA2BF,UAAW,4DAA2DA,UAAW,GAD3H;;SAIKC,UAAP;;;AC/BF;;;;;;;AAOA,AAAe,SAASL,KAAT,CAAe5F,IAAf,EAAqBU,OAArB,EAA8B;;MAEvC,CAACmF,mBAAmB7F,KAAKgE,QAAL,CAAcjE,SAAjC,EAA4C,OAA5C,EAAqD,cAArD,CAAL,EAA2E;WAClEC,IAAP;;;MAGEiE,eAAevD,QAAQzL,OAA3B;;;MAGI,OAAOgP,YAAP,KAAwB,QAA5B,EAAsC;mBACrBjE,KAAKgE,QAAL,CAAc3H,MAAd,CAAqB8J,aAArB,CAAmClC,YAAnC,CAAf;;;QAGI,CAACA,YAAL,EAAmB;aACVjE,IAAP;;GALJ,MAOO;;;QAGD,CAACA,KAAKgE,QAAL,CAAc3H,MAAd,CAAqBnE,QAArB,CAA8B+L,YAA9B,CAAL,EAAkD;cACxC3D,IAAR,CACE,+DADF;aAGON,IAAP;;;;QAIEnD,YAAYmD,KAAKnD,SAAL,CAAegB,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAlB;QACM,EAAExB,MAAF,EAAUlG,SAAV,KAAwB6J,KAAKhG,OAAnC;QACM6K,aAAa,CAAC,MAAD,EAAS,OAAT,EAAkB5Q,OAAlB,CAA0B4I,SAA1B,MAAyC,CAAC,CAA7D;;QAEMuJ,MAAMvB,aAAa,QAAb,GAAwB,OAApC;QACMwB,kBAAkBxB,aAAa,KAAb,GAAqB,MAA7C;QACMxM,OAAOgO,gBAAgBC,WAAhB,EAAb;QACMC,UAAU1B,aAAa,MAAb,GAAsB,KAAtC;QACM2B,SAAS3B,aAAa,QAAb,GAAwB,OAAvC;QACM4B,mBAAmBxI,cAAcgG,YAAd,EAA4BmC,GAA5B,CAAzB;;;;;;;;MAQIjQ,UAAUqQ,MAAV,IAAoBC,gBAApB,GAAuCpK,OAAOhE,IAAP,CAA3C,EAAyD;SAClD2B,OAAL,CAAaqC,MAAb,CAAoBhE,IAApB,KACEgE,OAAOhE,IAAP,KAAgBlC,UAAUqQ,MAAV,IAAoBC,gBAApC,CADF;;;MAIEtQ,UAAUkC,IAAV,IAAkBoO,gBAAlB,GAAqCpK,OAAOmK,MAAP,CAAzC,EAAyD;SAClDxM,OAAL,CAAaqC,MAAb,CAAoBhE,IAApB,KACElC,UAAUkC,IAAV,IAAkBoO,gBAAlB,GAAqCpK,OAAOmK,MAAP,CADvC;;OAGGxM,OAAL,CAAaqC,MAAb,GAAsBtC,cAAciG,KAAKhG,OAAL,CAAaqC,MAA3B,CAAtB;;;QAGMqK,SAASvQ,UAAUkC,IAAV,IAAkBlC,UAAUiQ,GAAV,IAAiB,CAAnC,GAAuCK,mBAAmB,CAAzE;;;;QAIMnR,MAAMN,yBAAyBgL,KAAKgE,QAAL,CAAc3H,MAAvC,CAAZ;QACMsK,mBAAmBnN,WAAWlE,IAAK,SAAQ+Q,eAAgB,EAA7B,CAAX,CAAzB;QACMO,mBAAmBpN,WAAWlE,IAAK,SAAQ+Q,eAAgB,OAA7B,CAAX,CAAzB;MACIQ,YACFH,SAAS1G,KAAKhG,OAAL,CAAaqC,MAAb,CAAoBhE,IAApB,CAAT,GAAqCsO,gBAArC,GAAwDC,gBAD1D;;;cAIYjN,KAAKC,GAAL,CAASD,KAAKmN,GAAL,CAASzK,OAAO+J,GAAP,IAAcK,gBAAvB,EAAyCI,SAAzC,CAAT,EAA8D,CAA9D,CAAZ;;OAEK5C,YAAL,GAAoBA,YAApB;OACKjK,OAAL,CAAa4L,KAAb,GAAqB;KAClBvN,IAAD,GAAQsB,KAAK4K,KAAL,CAAWsC,SAAX,CADW;KAElBN,OAAD,GAAW,EAFQ;GAArB;;SAKOvG,IAAP;;;ACvFF;;;;;;;AAOA,AAAe,SAAS+G,oBAAT,CAA8BnJ,SAA9B,EAAyC;MAClDA,cAAc,KAAlB,EAAyB;WAChB,OAAP;GADF,MAEO,IAAIA,cAAc,OAAlB,EAA2B;WACzB,KAAP;;SAEKA,SAAP;;;ACbF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,iBAAe,CACb,YADa,EAEb,MAFa,EAGb,UAHa,EAIb,WAJa,EAKb,KALa,EAMb,SANa,EAOb,aAPa,EAQb,OARa,EASb,WATa,EAUb,YAVa,EAWb,QAXa,EAYb,cAZa,EAab,UAba,EAcb,MAda,EAeb,YAfa,CAAf;;AC7BA;AACA,MAAMoJ,kBAAkBC,WAAW7G,KAAX,CAAiB,CAAjB,CAAxB;;;;;;;;;;;;AAYA,AAAe,SAAS8G,SAAT,CAAmBrK,SAAnB,EAA8BsK,UAAU,KAAxC,EAA+C;QACtDC,QAAQJ,gBAAgB/S,OAAhB,CAAwB4I,SAAxB,CAAd;QACMuC,MAAM4H,gBACT5G,KADS,CACHgH,QAAQ,CADL,EAETC,MAFS,CAEFL,gBAAgB5G,KAAhB,CAAsB,CAAtB,EAAyBgH,KAAzB,CAFE,CAAZ;SAGOD,UAAU/H,IAAIkI,OAAJ,EAAV,GAA0BlI,GAAjC;;;ACZF,MAAMmI,YAAY;QACV,MADU;aAEL,WAFK;oBAGE;CAHpB;;;;;;;;;AAaA,AAAe,SAAS3G,IAAT,CAAcZ,IAAd,EAAoBU,OAApB,EAA6B;;MAEtCQ,kBAAkBlB,KAAKgE,QAAL,CAAcjE,SAAhC,EAA2C,OAA3C,CAAJ,EAAyD;WAChDC,IAAP;;;MAGEA,KAAKwH,OAAL,IAAgBxH,KAAKnD,SAAL,KAAmBmD,KAAKa,iBAA5C,EAA+D;;WAEtDb,IAAP;;;QAGIxD,aAAaJ,cACjB4D,KAAKgE,QAAL,CAAc3H,MADG,EAEjB2D,KAAKgE,QAAL,CAAc7N,SAFG,EAGjBuK,QAAQpE,OAHS,EAIjBoE,QAAQnE,iBAJS,EAKjByD,KAAKW,aALY,CAAnB;;MAQI9D,YAAYmD,KAAKnD,SAAL,CAAegB,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAhB;MACI4J,oBAAoBnJ,qBAAqBzB,SAArB,CAAxB;MACIe,YAAYoC,KAAKnD,SAAL,CAAegB,KAAf,CAAqB,GAArB,EAA0B,CAA1B,KAAgC,EAAhD;;MAEI6J,YAAY,EAAhB;;UAEQhH,QAAQiH,QAAhB;SACOJ,UAAUK,IAAf;kBACc,CAAC/K,SAAD,EAAY4K,iBAAZ,CAAZ;;SAEGF,UAAUM,SAAf;kBACcX,UAAUrK,SAAV,CAAZ;;SAEG0K,UAAUO,gBAAf;kBACcZ,UAAUrK,SAAV,EAAqB,IAArB,CAAZ;;;kBAGY6D,QAAQiH,QAApB;;;YAGMtH,OAAV,CAAkB,CAAC0H,IAAD,EAAOX,KAAP,KAAiB;QAC7BvK,cAAckL,IAAd,IAAsBL,UAAU5T,MAAV,KAAqBsT,QAAQ,CAAvD,EAA0D;aACjDpH,IAAP;;;gBAGUA,KAAKnD,SAAL,CAAegB,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAZ;wBACoBS,qBAAqBzB,SAArB,CAApB;;UAEMgC,gBAAgBmB,KAAKhG,OAAL,CAAaqC,MAAnC;UACM2L,aAAahI,KAAKhG,OAAL,CAAa7D,SAAhC;;;UAGMqO,QAAQ7K,KAAK6K,KAAnB;UACMyD,cACHpL,cAAc,MAAd,IACC2H,MAAM3F,cAAc3F,KAApB,IAA6BsL,MAAMwD,WAAW/O,IAAjB,CAD/B,IAEC4D,cAAc,OAAd,IACC2H,MAAM3F,cAAc5F,IAApB,IAA4BuL,MAAMwD,WAAW9O,KAAjB,CAH9B,IAIC2D,cAAc,KAAd,IACC2H,MAAM3F,cAAc7F,MAApB,IAA8BwL,MAAMwD,WAAWjP,GAAjB,CALhC,IAMC8D,cAAc,QAAd,IACC2H,MAAM3F,cAAc9F,GAApB,IAA2ByL,MAAMwD,WAAWhP,MAAjB,CAR/B;;UAUMkP,gBAAgB1D,MAAM3F,cAAc5F,IAApB,IAA4BuL,MAAMhI,WAAWvD,IAAjB,CAAlD;UACMkP,iBAAiB3D,MAAM3F,cAAc3F,KAApB,IAA6BsL,MAAMhI,WAAWtD,KAAjB,CAApD;UACMkP,eAAe5D,MAAM3F,cAAc9F,GAApB,IAA2ByL,MAAMhI,WAAWzD,GAAjB,CAAhD;UACMsP,kBACJ7D,MAAM3F,cAAc7F,MAApB,IAA8BwL,MAAMhI,WAAWxD,MAAjB,CADhC;;UAGMsP,sBACHzL,cAAc,MAAd,IAAwBqL,aAAzB,IACCrL,cAAc,OAAd,IAAyBsL,cAD1B,IAECtL,cAAc,KAAd,IAAuBuL,YAFxB,IAGCvL,cAAc,QAAd,IAA0BwL,eAJ7B;;;UAOMxD,aAAa,CAAC,KAAD,EAAQ,QAAR,EAAkB5Q,OAAlB,CAA0B4I,SAA1B,MAAyC,CAAC,CAA7D;;;UAGM0L,wBACJ,CAAC,CAAC7H,QAAQ8H,cAAV,KACE3D,cAAcjH,cAAc,OAA5B,IAAuCsK,aAAxC,IACErD,cAAcjH,cAAc,KAA5B,IAAqCuK,cADvC,IAEE,CAACtD,UAAD,IAAejH,cAAc,OAA7B,IAAwCwK,YAF1C,IAGE,CAACvD,UAAD,IAAejH,cAAc,KAA7B,IAAsCyK,eAJzC,CADF;;;UAQMI,4BACJ,CAAC,CAAC/H,QAAQgI,uBAAV,KACE7D,cAAcjH,cAAc,OAA5B,IAAuCuK,cAAxC,IACEtD,cAAcjH,cAAc,KAA5B,IAAqCsK,aADvC,IAEE,CAACrD,UAAD,IAAejH,cAAc,OAA7B,IAAwCyK,eAF1C,IAGE,CAACxD,UAAD,IAAejH,cAAc,KAA7B,IAAsCwK,YAJzC,CADF;;UAOMO,mBAAmBJ,yBAAyBE,yBAAlD;;QAEIR,eAAeK,mBAAf,IAAsCK,gBAA1C,EAA4D;;WAErDnB,OAAL,GAAe,IAAf;;UAEIS,eAAeK,mBAAnB,EAAwC;oBAC1BZ,UAAUN,QAAQ,CAAlB,CAAZ;;;UAGEuB,gBAAJ,EAAsB;oBACR5B,qBAAqBnJ,SAArB,CAAZ;;;WAGGf,SAAL,GAAiBA,aAAae,YAAY,MAAMA,SAAlB,GAA8B,EAA3C,CAAjB;;;;WAIK5D,OAAL,CAAaqC,MAAb,gBACK2D,KAAKhG,OAAL,CAAaqC,MADlB,EAEKqC,iBACDsB,KAAKgE,QAAL,CAAc3H,MADb,EAED2D,KAAKhG,OAAL,CAAa7D,SAFZ,EAGD6J,KAAKnD,SAHJ,CAFL;;aASOiD,aAAaE,KAAKgE,QAAL,CAAcjE,SAA3B,EAAsCC,IAAtC,EAA4C,MAA5C,CAAP;;GAjFJ;SAoFOA,IAAP;;;AChJF;;;;;;;AAOA,AAAe,SAAS4I,YAAT,CAAsB5I,IAAtB,EAA4B;QACnC,EAAE3D,MAAF,EAAUlG,SAAV,KAAwB6J,KAAKhG,OAAnC;QACM6C,YAAYmD,KAAKnD,SAAL,CAAegB,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAlB;QACM2G,QAAQ7K,KAAK6K,KAAnB;QACMK,aAAa,CAAC,KAAD,EAAQ,QAAR,EAAkB5Q,OAAlB,CAA0B4I,SAA1B,MAAyC,CAAC,CAA7D;QACMxE,OAAOwM,aAAa,OAAb,GAAuB,QAApC;QACM2B,SAAS3B,aAAa,MAAb,GAAsB,KAArC;QACM5F,cAAc4F,aAAa,OAAb,GAAuB,QAA3C;;MAEIxI,OAAOhE,IAAP,IAAemM,MAAMrO,UAAUqQ,MAAV,CAAN,CAAnB,EAA6C;SACtCxM,OAAL,CAAaqC,MAAb,CAAoBmK,MAApB,IACEhC,MAAMrO,UAAUqQ,MAAV,CAAN,IAA2BnK,OAAO4C,WAAP,CAD7B;;MAGE5C,OAAOmK,MAAP,IAAiBhC,MAAMrO,UAAUkC,IAAV,CAAN,CAArB,EAA6C;SACtC2B,OAAL,CAAaqC,MAAb,CAAoBmK,MAApB,IAA8BhC,MAAMrO,UAAUkC,IAAV,CAAN,CAA9B;;;SAGK2H,IAAP;;;ACpBF;;;;;;;;;;;;AAYA,AAAO,SAAS6I,OAAT,CAAiBC,GAAjB,EAAsB7J,WAAtB,EAAmCJ,aAAnC,EAAkDF,gBAAlD,EAAoE;;QAEnEd,QAAQiL,IAAIlJ,KAAJ,CAAU,2BAAV,CAAd;QACMF,QAAQ,CAAC7B,MAAM,CAAN,CAAf;QACM8F,OAAO9F,MAAM,CAAN,CAAb;;;MAGI,CAAC6B,KAAL,EAAY;WACHoJ,GAAP;;;MAGEnF,KAAK1P,OAAL,CAAa,GAAb,MAAsB,CAA1B,EAA6B;QACvBgB,OAAJ;YACQ0O,IAAR;WACO,IAAL;kBACY9E,aAAV;;WAEG,GAAL;WACK,IAAL;;kBAEYF,gBAAV;;;UAGEjG,OAAOqB,cAAc9E,OAAd,CAAb;WACOyD,KAAKuG,WAAL,IAAoB,GAApB,GAA0BS,KAAjC;GAbF,MAcO,IAAIiE,SAAS,IAAT,IAAiBA,SAAS,IAA9B,EAAoC;;QAErCoF,IAAJ;QACIpF,SAAS,IAAb,EAAmB;aACVhK,KAAKC,GAAL,CACLnG,SAASmD,eAAT,CAAyB4D,YADpB,EAELhH,OAAOsI,WAAP,IAAsB,CAFjB,CAAP;KADF,MAKO;aACEnC,KAAKC,GAAL,CACLnG,SAASmD,eAAT,CAAyB2D,WADpB,EAEL/G,OAAOqI,UAAP,IAAqB,CAFhB,CAAP;;WAKKkN,OAAO,GAAP,GAAarJ,KAApB;GAdK,MAeA;;;WAGEA,KAAP;;;;;;;;;;;;;;;AAeJ,AAAO,SAASsJ,WAAT,CACLjN,MADK,EAEL8C,aAFK,EAGLF,gBAHK,EAILsK,aAJK,EAKL;QACMjP,UAAU,CAAC,CAAD,EAAI,CAAJ,CAAhB;;;;;QAKMkP,YAAY,CAAC,OAAD,EAAU,MAAV,EAAkBjV,OAAlB,CAA0BgV,aAA1B,MAA6C,CAAC,CAAhE;;;;QAIME,YAAYpN,OAAO8B,KAAP,CAAa,SAAb,EAAwBV,GAAxB,CAA4BiM,QAAQA,KAAKC,IAAL,EAApC,CAAlB;;;;QAIMC,UAAUH,UAAUlV,OAAV,CACdkL,KAAKgK,SAAL,EAAgBC,QAAQA,KAAKG,MAAL,CAAY,MAAZ,MAAwB,CAAC,CAAjD,CADc,CAAhB;;MAIIJ,UAAUG,OAAV,KAAsBH,UAAUG,OAAV,EAAmBrV,OAAnB,CAA2B,GAA3B,MAAoC,CAAC,CAA/D,EAAkE;YACxDqM,IAAR,CACE,8EADF;;;;;QAOIkJ,aAAa,aAAnB;MACIC,MAAMH,YAAY,CAAC,CAAb,GACN,CACEH,UACG/I,KADH,CACS,CADT,EACYkJ,OADZ,EAEGjC,MAFH,CAEU,CAAC8B,UAAUG,OAAV,EAAmBzL,KAAnB,CAAyB2L,UAAzB,EAAqC,CAArC,CAAD,CAFV,CADF,EAIE,CAACL,UAAUG,OAAV,EAAmBzL,KAAnB,CAAyB2L,UAAzB,EAAqC,CAArC,CAAD,EAA0CnC,MAA1C,CACE8B,UAAU/I,KAAV,CAAgBkJ,UAAU,CAA1B,CADF,CAJF,CADM,GASN,CAACH,SAAD,CATJ;;;QAYMM,IAAItM,GAAJ,CAAQ,CAACuM,EAAD,EAAKtC,KAAL,KAAe;;UAErBnI,cAAc,CAACmI,UAAU,CAAV,GAAc,CAAC8B,SAAf,GAA2BA,SAA5B,IAChB,QADgB,GAEhB,OAFJ;QAGIS,oBAAoB,KAAxB;WAEED;;;KAGGE,MAHH,CAGU,CAACtM,CAAD,EAAIC,CAAJ,KAAU;UACZD,EAAEA,EAAExJ,MAAF,GAAW,CAAb,MAAoB,EAApB,IAA0B,CAAC,GAAD,EAAM,GAAN,EAAWG,OAAX,CAAmBsJ,CAAnB,MAA0B,CAAC,CAAzD,EAA4D;UACxDD,EAAExJ,MAAF,GAAW,CAAb,IAAkByJ,CAAlB;4BACoB,IAApB;eACOD,CAAP;OAHF,MAIO,IAAIqM,iBAAJ,EAAuB;UAC1BrM,EAAExJ,MAAF,GAAW,CAAb,KAAmByJ,CAAnB;4BACoB,KAApB;eACOD,CAAP;OAHK,MAIA;eACEA,EAAE+J,MAAF,CAAS9J,CAAT,CAAP;;KAbN,EAeK,EAfL;;KAiBGJ,GAjBH,CAiBO2L,OAAOD,QAAQC,GAAR,EAAa7J,WAAb,EAA0BJ,aAA1B,EAAyCF,gBAAzC,CAjBd,CADF;GANI,CAAN;;;MA6BI0B,OAAJ,CAAY,CAACqJ,EAAD,EAAKtC,KAAL,KAAe;OACtB/G,OAAH,CAAW,CAAC+I,IAAD,EAAOS,MAAP,KAAkB;UACvBvG,UAAU8F,IAAV,CAAJ,EAAqB;gBACXhC,KAAR,KAAkBgC,QAAQM,GAAGG,SAAS,CAAZ,MAAmB,GAAnB,GAAyB,CAAC,CAA1B,GAA8B,CAAtC,CAAlB;;KAFJ;GADF;SAOO7P,OAAP;;;;;;;;;;;;AAYF,AAAe,SAAS+B,MAAT,CAAgBiE,IAAhB,EAAsB,EAAEjE,MAAF,EAAtB,EAAkC;QACzC,EAAEc,SAAF,EAAa7C,SAAS,EAAEqC,MAAF,EAAUlG,SAAV,EAAtB,KAAgD6J,IAAtD;QACMiJ,gBAAgBpM,UAAUgB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAtB;;MAEI7D,OAAJ;MACIsJ,UAAU,CAACvH,MAAX,CAAJ,EAAwB;cACZ,CAAC,CAACA,MAAF,EAAU,CAAV,CAAV;GADF,MAEO;cACKiN,YAAYjN,MAAZ,EAAoBM,MAApB,EAA4BlG,SAA5B,EAAuC8S,aAAvC,CAAV;;;MAGEA,kBAAkB,MAAtB,EAA8B;WACrBlQ,GAAP,IAAciB,QAAQ,CAAR,CAAd;WACOf,IAAP,IAAee,QAAQ,CAAR,CAAf;GAFF,MAGO,IAAIiP,kBAAkB,OAAtB,EAA+B;WAC7BlQ,GAAP,IAAciB,QAAQ,CAAR,CAAd;WACOf,IAAP,IAAee,QAAQ,CAAR,CAAf;GAFK,MAGA,IAAIiP,kBAAkB,KAAtB,EAA6B;WAC3BhQ,IAAP,IAAee,QAAQ,CAAR,CAAf;WACOjB,GAAP,IAAciB,QAAQ,CAAR,CAAd;GAFK,MAGA,IAAIiP,kBAAkB,QAAtB,EAAgC;WAC9BhQ,IAAP,IAAee,QAAQ,CAAR,CAAf;WACOjB,GAAP,IAAciB,QAAQ,CAAR,CAAd;;;OAGGqC,MAAL,GAAcA,MAAd;SACO2D,IAAP;;;AC5LF;;;;;;;AAOA,AAAe,SAAS8J,eAAT,CAAyB9J,IAAzB,EAA+BU,OAA/B,EAAwC;MACjDnE,oBACFmE,QAAQnE,iBAAR,IAA6B5F,gBAAgBqJ,KAAKgE,QAAL,CAAc3H,MAA9B,CAD/B;;;;;MAMI2D,KAAKgE,QAAL,CAAc7N,SAAd,KAA4BoG,iBAAhC,EAAmD;wBAC7B5F,gBAAgB4F,iBAAhB,CAApB;;;;;;QAMIwN,gBAAgBzI,yBAAyB,WAAzB,CAAtB;QACM0I,eAAehK,KAAKgE,QAAL,CAAc3H,MAAd,CAAqBwF,KAA1C,CAfqD;QAgB/C,EAAE9I,GAAF,EAAOE,IAAP,EAAa,CAAC8Q,aAAD,GAAiBE,SAA9B,KAA4CD,YAAlD;eACajR,GAAb,GAAmB,EAAnB;eACaE,IAAb,GAAoB,EAApB;eACa8Q,aAAb,IAA8B,EAA9B;;QAEMvN,aAAaJ,cACjB4D,KAAKgE,QAAL,CAAc3H,MADG,EAEjB2D,KAAKgE,QAAL,CAAc7N,SAFG,EAGjBuK,QAAQpE,OAHS,EAIjBC,iBAJiB,EAKjByD,KAAKW,aALY,CAAnB;;;;eAUa5H,GAAb,GAAmBA,GAAnB;eACaE,IAAb,GAAoBA,IAApB;eACa8Q,aAAb,IAA8BE,SAA9B;;UAEQzN,UAAR,GAAqBA,UAArB;;QAEMjF,QAAQmJ,QAAQwJ,QAAtB;MACI7N,SAAS2D,KAAKhG,OAAL,CAAaqC,MAA1B;;QAEMgD,QAAQ;YACJxC,SAAR,EAAmB;UACb6C,QAAQrD,OAAOQ,SAAP,CAAZ;UAEER,OAAOQ,SAAP,IAAoBL,WAAWK,SAAX,CAApB,IACA,CAAC6D,QAAQyJ,mBAFX,EAGE;gBACQxQ,KAAKC,GAAL,CAASyC,OAAOQ,SAAP,CAAT,EAA4BL,WAAWK,SAAX,CAA5B,CAAR;;aAEK,EAAE,CAACA,SAAD,GAAa6C,KAAf,EAAP;KATU;cAWF7C,SAAV,EAAqB;YACbkC,WAAWlC,cAAc,OAAd,GAAwB,MAAxB,GAAiC,KAAlD;UACI6C,QAAQrD,OAAO0C,QAAP,CAAZ;UAEE1C,OAAOQ,SAAP,IAAoBL,WAAWK,SAAX,CAApB,IACA,CAAC6D,QAAQyJ,mBAFX,EAGE;gBACQxQ,KAAKmN,GAAL,CACNzK,OAAO0C,QAAP,CADM,EAENvC,WAAWK,SAAX,KACGA,cAAc,OAAd,GAAwBR,OAAOpC,KAA/B,GAAuCoC,OAAOnC,MADjD,CAFM,CAAR;;aAMK,EAAE,CAAC6E,QAAD,GAAYW,KAAd,EAAP;;GAxBJ;;QA4BMW,OAAN,CAAcxD,aAAa;UACnBxE,OACJ,CAAC,MAAD,EAAS,KAAT,EAAgBpE,OAAhB,CAAwB4I,SAAxB,MAAuC,CAAC,CAAxC,GAA4C,SAA5C,GAAwD,WAD1D;0BAEcR,MAAd,EAAyBgD,MAAMhH,IAAN,EAAYwE,SAAZ,CAAzB;GAHF;;OAMK7C,OAAL,CAAaqC,MAAb,GAAsBA,MAAtB;;SAEO2D,IAAP;;;ACvFF;;;;;;;AAOA,AAAe,SAASoK,KAAT,CAAepK,IAAf,EAAqB;QAC5BnD,YAAYmD,KAAKnD,SAAvB;QACMoM,gBAAgBpM,UAAUgB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAtB;QACMwM,iBAAiBxN,UAAUgB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAvB;;;MAGIwM,cAAJ,EAAoB;UACZ,EAAElU,SAAF,EAAakG,MAAb,KAAwB2D,KAAKhG,OAAnC;UACM6K,aAAa,CAAC,QAAD,EAAW,KAAX,EAAkB5Q,OAAlB,CAA0BgV,aAA1B,MAA6C,CAAC,CAAjE;UACM5Q,OAAOwM,aAAa,MAAb,GAAsB,KAAnC;UACM5F,cAAc4F,aAAa,OAAb,GAAuB,QAA3C;;UAEMyF,eAAe;aACZ,EAAE,CAACjS,IAAD,GAAQlC,UAAUkC,IAAV,CAAV,EADY;WAEd;SACFA,IAAD,GAAQlC,UAAUkC,IAAV,IAAkBlC,UAAU8I,WAAV,CAAlB,GAA2C5C,OAAO4C,WAAP;;KAHvD;;SAOKjF,OAAL,CAAaqC,MAAb,gBAA2BA,MAA3B,EAAsCiO,aAAaD,cAAb,CAAtC;;;SAGKrK,IAAP;;;AC1BF;;;;;;;AAOA,AAAe,SAASuK,IAAT,CAAcvK,IAAd,EAAoB;MAC7B,CAAC6F,mBAAmB7F,KAAKgE,QAAL,CAAcjE,SAAjC,EAA4C,MAA5C,EAAoD,iBAApD,CAAL,EAA6E;WACpEC,IAAP;;;QAGIlD,UAAUkD,KAAKhG,OAAL,CAAa7D,SAA7B;QACMqU,QAAQrL,KACZa,KAAKgE,QAAL,CAAcjE,SADF,EAEZjH,YAAYA,SAASuI,IAAT,KAAkB,iBAFlB,EAGZ7E,UAHF;;MAMEM,QAAQ9D,MAAR,GAAiBwR,MAAMzR,GAAvB,IACA+D,QAAQ7D,IAAR,GAAeuR,MAAMtR,KADrB,IAEA4D,QAAQ/D,GAAR,GAAcyR,MAAMxR,MAFpB,IAGA8D,QAAQ5D,KAAR,GAAgBsR,MAAMvR,IAJxB,EAKE;;QAEI+G,KAAKuK,IAAL,KAAc,IAAlB,EAAwB;aACfvK,IAAP;;;SAGGuK,IAAL,GAAY,IAAZ;SACK1G,UAAL,CAAgB,qBAAhB,IAAyC,EAAzC;GAZF,MAaO;;QAED7D,KAAKuK,IAAL,KAAc,KAAlB,EAAyB;aAChBvK,IAAP;;;SAGGuK,IAAL,GAAY,KAAZ;SACK1G,UAAL,CAAgB,qBAAhB,IAAyC,KAAzC;;;SAGK7D,IAAP;;;ACzCF;;;;;;;AAOA,AAAe,SAASyK,KAAT,CAAezK,IAAf,EAAqB;QAC5BnD,YAAYmD,KAAKnD,SAAvB;QACMoM,gBAAgBpM,UAAUgB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAtB;QACM,EAAExB,MAAF,EAAUlG,SAAV,KAAwB6J,KAAKhG,OAAnC;QACM8E,UAAU,CAAC,MAAD,EAAS,OAAT,EAAkB7K,OAAlB,CAA0BgV,aAA1B,MAA6C,CAAC,CAA9D;;QAEMyB,iBAAiB,CAAC,KAAD,EAAQ,MAAR,EAAgBzW,OAAhB,CAAwBgV,aAAxB,MAA2C,CAAC,CAAnE;;SAEOnK,UAAU,MAAV,GAAmB,KAA1B,IACE3I,UAAU8S,aAAV,KACCyB,iBAAiBrO,OAAOyC,UAAU,OAAV,GAAoB,QAA3B,CAAjB,GAAwD,CADzD,CADF;;OAIKjC,SAAL,GAAiByB,qBAAqBzB,SAArB,CAAjB;OACK7C,OAAL,CAAaqC,MAAb,GAAsBtC,cAAcsC,MAAd,CAAtB;;SAEO2D,IAAP;;;ACdF;;;;;;;;;;;;;;;;;;;;;AAqBA,gBAAe;;;;;;;;;SASN;;WAEE,GAFF;;aAII,IAJJ;;QAMDoK;GAfO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAwDL;;WAEC,GAFD;;aAIG,IAJH;;QAMFrO,MANE;;;;YAUE;GAlEG;;;;;;;;;;;;;;;;;;;mBAsFI;;WAER,GAFQ;;aAIN,IAJM;;QAMX+N,eANW;;;;;;cAYL,CAAC,MAAD,EAAS,OAAT,EAAkB,KAAlB,EAAyB,QAAzB,CAZK;;;;;;;aAmBN,CAnBM;;;;;;uBAyBI;GA/GR;;;;;;;;;;;gBA2HC;;WAEL,GAFK;;aAIH,IAJG;;QAMRlB;GAjIO;;;;;;;;;;;;SA8IN;;WAEE,GAFF;;aAII,IAJJ;;QAMDhD,KANC;;aAQI;GAtJE;;;;;;;;;;;;;QAoKP;;WAEG,GAFH;;aAIK,IAJL;;QAMAhF,IANA;;;;;;;cAaM,MAbN;;;;;aAkBK,CAlBL;;;;;;;uBAyBe,UAzBf;;;;;;;;oBAiCY,KAjCZ;;;;;;;;6BAyCqB;GA7Md;;;;;;;;;SAuNN;;WAEE,GAFF;;aAII,KAJJ;;QAMD6J;GA7NO;;;;;;;;;;;;QA0OP;;WAEG,GAFH;;aAIK,IAJL;;QAMAF;GAhPO;;;;;;;;;;;;;;;;;gBAkQC;;WAEL,GAFK;;aAIH,IAJG;;QAMRnF,YANQ;;;;;;qBAYK,IAZL;;;;;;OAkBT,QAlBS;;;;;;OAwBT;GA1RQ;;;;;;;;;;;;;;;;;cA4SD;;WAEH,GAFG;;aAID,IAJC;;QAMNrB,UANM;;YAQFI,gBARE;;;;;;;qBAeOhE;;CA3TrB;;;;;;;;;;;;;;;;;;;;;AC9BA;;;;;;;;;;;;;;;;AAgBA,eAAe;;;;;aAKF,QALE;;;;;;iBAWE,KAXF;;;;;;iBAiBE,IAjBF;;;;;;;mBAwBI,KAxBJ;;;;;;;;YAgCH,MAAM,EAhCH;;;;;;;;;;YA0CH,MAAM,EA1CH;;;;;;;;CAAf;;;;;;;;;;;;AClBA;AACA,AAGA;AACA,AAOe,MAAMwK,MAAN,CAAa;;;;;;;;;cASdxU,SAAZ,EAAuBkG,MAAvB,EAA+BqE,UAAU,EAAzC,EAA6C;SAyF7CyC,cAzF6C,GAyF5B,MAAMyH,sBAAsB,KAAKpK,MAA3B,CAzFsB;;;SAEtCA,MAAL,GAAcqK,SAAS,KAAKrK,MAAL,CAAYsK,IAAZ,CAAiB,IAAjB,CAAT,CAAd;;;SAGKpK,OAAL,gBAAoBiK,OAAOI,QAA3B,EAAwCrK,OAAxC;;;SAGK3C,KAAL,GAAa;mBACE,KADF;iBAEA,KAFA;qBAGI;KAHjB;;;SAOK5H,SAAL,GAAiBA,aAAaA,UAAU6U,MAAvB,GAAgC7U,UAAU,CAAV,CAAhC,GAA+CA,SAAhE;SACKkG,MAAL,GAAcA,UAAUA,OAAO2O,MAAjB,GAA0B3O,OAAO,CAAP,CAA1B,GAAsCA,MAApD;;;SAGKqE,OAAL,CAAaX,SAAb,GAAyB,EAAzB;WACO7C,IAAP,cACKyN,OAAOI,QAAP,CAAgBhL,SADrB,EAEKW,QAAQX,SAFb,GAGGM,OAHH,CAGWgB,QAAQ;WACZX,OAAL,CAAaX,SAAb,CAAuBsB,IAAvB,iBAEMsJ,OAAOI,QAAP,CAAgBhL,SAAhB,CAA0BsB,IAA1B,KAAmC,EAFzC,EAIMX,QAAQX,SAAR,GAAoBW,QAAQX,SAAR,CAAkBsB,IAAlB,CAApB,GAA8C,EAJpD;KAJF;;;SAaKtB,SAAL,GAAiB9C,OAAOC,IAAP,CAAY,KAAKwD,OAAL,CAAaX,SAAzB,EACd5C,GADc,CACVkE;;OAEA,KAAKX,OAAL,CAAaX,SAAb,CAAuBsB,IAAvB,CAFA,CADU;;KAMdhE,IANc,CAMT,CAACC,CAAD,EAAIC,CAAJ,KAAUD,EAAE/F,KAAF,GAAUgG,EAAEhG,KANb,CAAjB;;;;;;SAYKwI,SAAL,CAAeM,OAAf,CAAuB+D,mBAAmB;UACpCA,gBAAgB7D,OAAhB,IAA2B5L,WAAWyP,gBAAgB6G,MAA3B,CAA/B,EAAmE;wBACjDA,MAAhB,CACE,KAAK9U,SADP,EAEE,KAAKkG,MAFP,EAGE,KAAKqE,OAHP,EAIE0D,eAJF,EAKE,KAAKrG,KALP;;KAFJ;;;SAaKyC,MAAL;;UAEMyC,gBAAgB,KAAKvC,OAAL,CAAauC,aAAnC;QACIA,aAAJ,EAAmB;;WAEZC,oBAAL;;;SAGGnF,KAAL,CAAWkF,aAAX,GAA2BA,aAA3B;;;;;WAKO;WACAzC,OAAOzL,IAAP,CAAY,IAAZ,CAAP;;YAEQ;WACD+M,QAAQ/M,IAAR,CAAa,IAAb,CAAP;;yBAEqB;WACdmO,qBAAqBnO,IAArB,CAA0B,IAA1B,CAAP;;0BAEsB;WACfkN,sBAAsBlN,IAAtB,CAA2B,IAA3B,CAAP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1FiB4V,OAoHZO,QAAQ,CAAC,OAAO1X,MAAP,KAAkB,WAAlB,GAAgCA,MAAhC,GAAyC2X,MAA1C,EAAkDC;AApH9CT,OAsHZ1D,aAAaA;AAtHD0D,OAwHZI,WAAWA;;;;"}