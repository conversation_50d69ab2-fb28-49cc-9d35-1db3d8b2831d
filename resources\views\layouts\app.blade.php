<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-bs-theme="light">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', config('app.name', 'Laravel'))</title>
    
    <!-- CSS files -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/css/tabler.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg" rel="stylesheet"/>
    
    <!-- Custom CSS -->
    <style>
        /* Theme transition for smooth switching */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Theme toggle button styling */
        .theme-toggle {
            cursor: pointer;
            border: none;
            background: none;
            padding: 0.5rem;
            border-radius: var(--tblr-border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--tblr-body-color);
            transition: background-color 0.2s ease, transform 0.15s ease;
        }

        .theme-toggle:hover {
            background-color: var(--tblr-secondary-bg);
        }

        .theme-toggle:active {
            transform: scale(0.95);
        }

        /* Dynamic icon visibility based on theme */
        .theme-icon-light {
            display: block;
        }

        .theme-icon-dark {
            display: none;
        }

        [data-bs-theme="dark"] .theme-icon-light {
            display: none;
        }

        [data-bs-theme="dark"] .theme-icon-dark {
            display: block;
        }

        /* Ensure proper styling for cards and components */
        .card {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
            box-shadow: var(--tblr-box-shadow);
        }

        /* Navbar styling */
        .navbar {
            background-color: var(--tblr-bg-surface);
            border-bottom: 1px solid var(--tblr-border-color);
        }

        .navbar .navbar-brand,
        .navbar .nav-link {
            color: var(--tblr-body-color);
        }

        /* Form controls */
        .form-control {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
            color: var(--tblr-body-color);
        }

        .form-control:focus {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-primary);
            box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);
        }

        /* Buttons */
        .btn-outline-primary,
        .btn-outline-secondary,
        .btn-outline-info {
            background-color: var(--tblr-bg-surface);
            color: var(--tblr-body-color);
            border-color: var(--tblr-border-color);
        }

        .btn-outline-primary:hover,
        .btn-outline-secondary:hover,
        .btn-outline-info:hover {
            background-color: var(--tblr-secondary-bg);
        }

        /* Dropdown menus */
        .dropdown-menu {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
            box-shadow: var(--tblr-box-shadow-lg);
        }

        .dropdown-item {
            color: var(--tblr-body-color);
        }

        .dropdown-item:hover {
            background-color: var(--tblr-secondary-bg);
        }

        /* Tables */
        .table {
            color: var(--tblr-body-color);
        }

        .table th,
        .table td {
            border-color: var(--tblr-border-color);
        }

        /* Modals */
        .modal-content {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
        }

        .modal-header {
            border-bottom-color: var(--tblr-border-color);
        }

        .modal-footer {
            border-top-color: var(--tblr-border-color);
        }

        /* List groups */
        .list-group-item {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
            color: var(--tblr-body-color);
        }

        /* Alerts */
        .alert {
            border-color: var(--tblr-border-color);
        }

        /* Pagination */
        .page-link {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
            color: var(--tblr-body-color);
        }

        .page-link:hover {
            background-color: var(--tblr-secondary-bg);
            border-color: var(--tblr-border-color);
        }

        .page-item.active .page-link {
            background-color: var(--tblr-primary);
            border-color: var(--tblr-primary);
        }

        /* Progress bars */
        .progress {
            background-color: var(--tblr-secondary-bg);
        }

        /* Tooltips and popovers */
        .tooltip-inner {
            background-color: var(--tblr-bg-surface);
            color: var(--tblr-body-color);
        }

        .popover {
            background-color: var(--tblr-bg-surface);
            border-color: var(--tblr-border-color);
        }

        .popover-body {
            color: var(--tblr-body-color);
        }

        /* Additional fixes for better dark mode support */
        .text-muted {
            color: var(--tblr-secondary-color) !important;
        }

        .page-header {
            background-color: var(--tblr-body-bg);
            border-bottom: 1px solid var(--tblr-border-color);
        }

        .avatar {
            border: 2px solid var(--tblr-border-color);
        }

        /* Ensure icons inherit proper colors */
        .icon {
            color: inherit;
        }
        
        /* Hide icons based on theme */
        [data-theme="light"] .theme-icon-dark,
        [data-theme="dark"] .theme-icon-light {
            display: none;
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <div class="page">
        <!-- Navbar -->
        <header class="navbar navbar-expand-md navbar-light d-print-none">
            <div class="container-xl">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                    <a href="{{ url('/') }}">
                        {{ config('app.name', 'Laravel') }}
                    </a>
                </h1>

                <div class="collapse navbar-collapse" id="navbar-menu">
                    <div class="d-flex flex-column flex-md-row flex-fill align-items-stretch align-items-md-center">
                        <ul class="navbar-nav">
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/') }}">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <polyline points="5 12 3 12 12 3 21 12 19 12"></polyline>
                                            <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"></path>
                                            <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"></path>
                                        </svg>
                                    </span>
                                    <span class="nav-link-title">Home</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/tabler') }}">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <rect x="4" y="4" width="6" height="6" rx="1"></rect>
                                            <rect x="14" y="4" width="6" height="6" rx="1"></rect>
                                            <rect x="4" y="14" width="6" height="6" rx="1"></rect>
                                            <rect x="14" y="14" width="6" height="6" rx="1"></rect>
                                        </svg>
                                    </span>
                                    <span class="nav-link-title">Dashboard</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url('/components') }}">
                                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                                        <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <rect x="4" y="4" width="16" height="16" rx="2"></rect>
                                            <rect x="9" y="9" width="6" height="6"></rect>
                                            <line x1="9" y1="1" x2="9" y2="4"></line>
                                            <line x1="15" y1="1" x2="15" y2="4"></line>
                                            <line x1="9" y1="20" x2="9" y2="23"></line>
                                            <line x1="15" y1="20" x2="15" y2="23"></line>
                                            <line x1="20" y1="9" x2="23" y2="9"></line>
                                            <line x1="20" y1="14" x2="23" y2="14"></line>
                                            <line x1="1" y1="9" x2="4" y2="9"></line>
                                            <line x1="1" y1="14" x2="4" y2="14"></line>
                                        </svg>
                                    </span>
                                    <span class="nav-link-title">Components</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="navbar-nav flex-row order-md-last">
                    <!-- Theme Toggle -->
                    <div class="nav-item">
                        <button class="theme-toggle" onclick="toggleTheme()" title="Toggle theme">
                            <!-- Light mode icon (sun) - shown in light mode -->
                            <svg class="theme-icon-light" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="5"></circle>
                                <line x1="12" y1="1" x2="12" y2="3"></line>
                                <line x1="12" y1="21" x2="12" y2="23"></line>
                                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                                <line x1="1" y1="12" x2="3" y2="12"></line>
                                <line x1="21" y1="12" x2="23" y2="12"></line>
                                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                            </svg>
                            <!-- Dark mode icon (moon) - shown in dark mode -->
                            <svg class="theme-icon-dark" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-link-icon d-md-none d-lg-inline-block">
                                <svg class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M9 7 h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3"></path>
                                    <path d="M9 15h3l8.5 -8.5a1.5 1.5 0 0 0 -3 -3l-8.5 8.5v3"></path>
                                    <path d="M16 5l3 3"></path>
                                </svg>
                            </span>
                            <span class="nav-link-title">Demo User</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Page wrapper -->
        <div class="page-wrapper">
            <!-- Page header -->
            @hasSection('page-header')
            <div class="page-header d-print-none">
                <div class="container-xl">
                    <div class="row g-2 align-items-center">
                        <div class="col">
                            @yield('page-header')
                        </div>
                    </div>
                </div>
            </div>
            @endif
            
            <!-- Page body -->
            <div class="page-body">
                <div class="container-xl">
                    @if(session('success'))
                    <div class="alert alert-success alert-dismissible" role="alert">
                        <div class="d-flex">
                            <div>
                                <svg class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M5 12l5 5l10 -10"></path>
                                </svg>
                            </div>
                            <div>{{ session('success') }}</div>
                        </div>
                        <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                    </div>
                    @endif
                    
                    @if(session('error'))
                    <div class="alert alert-danger alert-dismissible" role="alert">
                        <div class="d-flex">
                            <div>
                                <svg class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M12 9v2m0 4v.01"></path>
                                    <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                                </svg>
                            </div>
                            <div>{{ session('error') }}</div>
                        </div>
                        <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                    </div>
                    @endif
                    
                    @yield('content')
                </div>
            </div>
            
            <!-- Page footer -->
            <footer class="footer footer-transparent d-print-none">
                <div class="container-xl">
                    <div class="row text-center align-items-center flex-row-reverse">
                        <div class="col-lg-auto ms-lg-auto">
                            <ul class="list-inline list-inline-dots mb-0">
                                <li class="list-inline-item">
                                    <a href="https://tabler.io" target="_blank" class="link-secondary">
                                        Powered by Tabler
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                            <ul class="list-inline list-inline-dots mb-0">
                                <li class="list-inline-item">
                                    Copyright &copy; {{ date('Y') }}
                                    <a href="{{ url('/') }}" class="link-secondary">{{ config('app.name') }}</a>.
                                    All rights reserved.
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- Tabler Core -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/js/tabler.min.js"></script>
    
    <!-- Theme Toggle Script -->
    <script>
        // Theme management using Bootstrap's data-bs-theme
        function getStoredTheme() {
            return localStorage.getItem('theme') || 'light';
        }

        function setStoredTheme(theme) {
            localStorage.setItem('theme', theme);
        }

        function setTheme(theme) {
            // Set theme on both html and body elements for maximum compatibility
            document.documentElement.setAttribute('data-bs-theme', theme);
            document.body.setAttribute('data-bs-theme', theme);
            setStoredTheme(theme);

            // Update button title
            const toggleButton = document.querySelector('.theme-toggle');
            if (toggleButton) {
                toggleButton.title = theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode';
            }

            // Dispatch custom event for other components to listen
            window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));

            console.log('Theme set to:', theme); // Debug log
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-bs-theme') || 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);

            // Add visual feedback with better animation
            const toggleButton = document.querySelector('.theme-toggle');
            if (toggleButton) {
                toggleButton.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    toggleButton.style.transform = 'scale(1)';
                }, 100);
            }
        }

        // Get preferred theme from system or storage
        function getPreferredTheme() {
            const storedTheme = getStoredTheme();
            if (storedTheme) {
                return storedTheme;
            }

            // Check system preference
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                return 'dark';
            }

            return 'light';
        }

        // Initialize theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const preferredTheme = getPreferredTheme();
            setTheme(preferredTheme);

            console.log('Theme initialized:', preferredTheme); // Debug log
        });

        // Listen for system theme changes
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

            const handleThemeChange = (e) => {
                // Only auto-switch if user hasn't manually set a preference
                if (!localStorage.getItem('theme')) {
                    setTheme(e.matches ? 'dark' : 'light');
                }
            };

            // Use modern addEventListener if available
            if (mediaQuery.addEventListener) {
                mediaQuery.addEventListener('change', handleThemeChange);
            } else {
                // Fallback for older browsers
                mediaQuery.addListener(handleThemeChange);
            }
        }

        // Keyboard shortcut for theme toggle (Ctrl/Cmd + Shift + T)
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                toggleTheme();
            }
        });

        // Expose functions globally for debugging
        window.themeUtils = {
            getStoredTheme,
            setTheme,
            toggleTheme,
            getPreferredTheme
        };
    </script>
    
    @stack('scripts')
</body>
</html>
