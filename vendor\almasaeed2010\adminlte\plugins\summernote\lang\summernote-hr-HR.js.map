{"version": 3, "file": "lang/summernote-hr-HR.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,YADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,WAHP;AAIJC,QAAAA,KAAK,EAAE,sBAJH;AAKJC,QAAAA,MAAM,EAAE,eALJ;AAMJC,QAAAA,IAAI,EAAE,aANF;AAOJC,QAAAA,aAAa,EAAE,WAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,MAAM,EAAE,aAFH;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,eAJP;AAKLC,QAAAA,aAAa,EAAE,eALV;AAMLC,QAAAA,SAAS,EAAE,iBANN;AAOLC,QAAAA,UAAU,EAAE,gBAPP;AAQLC,QAAAA,SAAS,EAAE,gBARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,oBAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,qBAfZ;AAgBLC,QAAAA,eAAe,EAAE,mBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,cAlBA;AAmBLC,QAAAA,MAAM,EAAE,cAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,eAFN;AAGLpB,QAAAA,MAAM,EAAE,aAHH;AAILgB,QAAAA,GAAG,EAAE,WAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,YAFJ;AAGJuB,QAAAA,MAAM,EAAE,aAHJ;AAIJC,QAAAA,IAAI,EAAE,OAJF;AAKJC,QAAAA,aAAa,EAAE,iBALX;AAMJT,QAAAA,GAAG,EAAE,iBAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,KAFE;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,cADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,sBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,kBAFA;AAGTC,QAAAA,MAAM,EAAE,mBAHC;AAITC,QAAAA,IAAI,EAAE,iBAJG;AAKTC,QAAAA,MAAM,EAAE,YALC;AAMTC,QAAAA,KAAK,EAAE,gBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,iBADH;AAELC,QAAAA,IAAI,EAAE,WAFD;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,aAJP;AAKLC,QAAAA,WAAW,EAAE,UALR;AAMLC,QAAAA,cAAc,EAAE,UANX;AAOLC,QAAAA,KAAK,EAAE,SAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,sBADH;AAERC,QAAAA,KAAK,EAAE,SAFC;AAGRC,QAAAA,cAAc,EAAE,qBAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,wBALb;AAMRC,QAAAA,aAAa,EAAE,gBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-hr-HR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'hr-HR': {\n      font: {\n        bold: 'Podebljano',\n        italic: 'Kurz<PERSON>',\n        underline: 'Podvu<PERSON><PERSON>',\n        clear: 'Ukloni stilove fonta',\n        height: 'Visina linije',\n        name: '<PERSON>ont Family',\n        strikethrough: 'Precrtano',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Veličina fonta',\n      },\n      image: {\n        image: 'Slika',\n        insert: 'Ubaci sliku',\n        resizeFull: 'Puna veličina',\n        resizeHalf: 'Umanji na 50%',\n        resizeQuarter: '<PERSON><PERSON><PERSON> na 25%',\n        floatLeft: 'Poravnaj lijevo',\n        floatRight: '<PERSON>ravnaj desno',\n        floatNone: 'Bez poravnanja',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Povuci sliku ovdje',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Izaberi iz datoteke',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'Adresa slike',\n        remove: 'Ukloni sliku',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Veza na video',\n        insert: 'Ubaci video',\n        url: 'URL video',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion ili Youku)',\n      },\n      link: {\n        link: 'Veza',\n        insert: 'Ubaci vezu',\n        unlink: 'Ukloni vezu',\n        edit: 'Uredi',\n        textToDisplay: 'Tekst za prikaz',\n        url: 'Internet adresa',\n        openInNewWindow: 'Otvori u novom prozoru',\n      },\n      table: {\n        table: 'Tablica',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Ubaci horizontalnu liniju',\n      },\n      style: {\n        style: 'Stil',\n        p: 'pni',\n        blockquote: 'Citat',\n        pre: 'Kôd',\n        h1: 'Naslov 1',\n        h2: 'Naslov 2',\n        h3: 'Naslov 3',\n        h4: 'Naslov 4',\n        h5: 'Naslov 5',\n        h6: 'Naslov 6',\n      },\n      lists: {\n        unordered: 'Obična lista',\n        ordered: 'Numerirana lista',\n      },\n      options: {\n        help: 'Pomoć',\n        fullscreen: 'Preko cijelog ekrana',\n        codeview: 'Izvorni kôd',\n      },\n      paragraph: {\n        paragraph: 'Paragraf',\n        outdent: 'Smanji uvlačenje',\n        indent: 'Povećaj uvlačenje',\n        left: 'Poravnaj lijevo',\n        center: 'Centrirano',\n        right: 'Poravnaj desno',\n        justify: 'Poravnaj obostrano',\n      },\n      color: {\n        recent: 'Posljednja boja',\n        more: 'Više boja',\n        background: 'Boja pozadine',\n        foreground: 'Boja teksta',\n        transparent: 'Prozirna',\n        setTransparent: 'Prozirna',\n        reset: 'Poništi',\n        resetToDefault: 'Podrazumijevana',\n      },\n      shortcut: {\n        shortcuts: 'Prečice s tipkovnice',\n        close: 'Zatvori',\n        textFormatting: 'Formatiranje teksta',\n        action: 'Akcija',\n        paragraphFormatting: 'Formatiranje paragrafa',\n        documentStyle: 'Stil dokumenta',\n        extraKeys: 'Dodatne kombinacije',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Poništi',\n        redo: 'Ponovi',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}