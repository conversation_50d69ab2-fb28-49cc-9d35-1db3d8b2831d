{"version": 3, "file": "lang/summernote-pt-PT.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,SADF;AAEJC,QAAAA,MAAM,EAAE,SAFJ;AAGJC,QAAAA,SAAS,EAAE,YAHP;AAIJC,QAAAA,KAAK,EAAE,yBAJH;AAKJC,QAAAA,MAAM,EAAE,iBALJ;AAMJC,QAAAA,IAAI,EAAE,OANF;AAOJC,QAAAA,aAAa,EAAE,SAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,MAAM,EAAE,gBAFH;AAGLC,QAAAA,UAAU,EAAE,wBAHP;AAILC,QAAAA,UAAU,EAAE,sBAJP;AAKLC,QAAAA,aAAa,EAAE,yBALV;AAMLC,QAAAA,SAAS,EAAE,gBANN;AAOLC,QAAAA,UAAU,EAAE,eAPP;AAQLC,QAAAA,SAAS,EAAE,WARN;AASLC,QAAAA,YAAY,EAAE,oBATT;AAULC,QAAAA,WAAW,EAAE,gBAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,eAZN;AAaLC,QAAAA,aAAa,EAAE,8BAbV;AAcLC,QAAAA,SAAS,EAAE,6BAdN;AAeLC,QAAAA,eAAe,EAAE,iCAfZ;AAgBLC,QAAAA,eAAe,EAAE,2BAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,oDAjBjB;AAkBLC,QAAAA,GAAG,EAAE,oBAlBA;AAmBLC,QAAAA,MAAM,EAAE,gBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,iBAFN;AAGLpB,QAAAA,MAAM,EAAE,eAHH;AAILgB,QAAAA,GAAG,EAAE,eAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,iBAFJ;AAGJuB,QAAAA,MAAM,EAAE,iBAHJ;AAIJC,QAAAA,IAAI,EAAE,QAJF;AAKJC,QAAAA,aAAa,EAAE,mBALX;AAMJT,QAAAA,GAAG,EAAE,iCAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,WAAW,EAAE,uBAFR;AAGLC,QAAAA,WAAW,EAAE,wBAHR;AAILC,QAAAA,UAAU,EAAE,6BAJP;AAKLC,QAAAA,WAAW,EAAE,6BALR;AAMLC,QAAAA,MAAM,EAAE,eANH;AAOLC,QAAAA,MAAM,EAAE,gBAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,CAAC,EAAE,WAFE;AAGLC,QAAAA,UAAU,EAAE,SAHP;AAILC,QAAAA,GAAG,EAAE,QAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,sBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,iBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,WADF;AAETC,QAAAA,OAAO,EAAE,iBAFA;AAGTC,QAAAA,MAAM,EAAE,iBAHC;AAITC,QAAAA,IAAI,EAAE,oBAJG;AAKTC,QAAAA,MAAM,EAAE,mBALC;AAMTC,QAAAA,KAAK,EAAE,kBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,aADH;AAELC,QAAAA,IAAI,EAAE,YAFD;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,UAAU,EAAE,OAJP;AAKLC,QAAAA,WAAW,EAAE,cALR;AAMLC,QAAAA,cAAc,EAAE,oBANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE,kBARX;AASLC,QAAAA,QAAQ,EAAE;AATL,OA9FA;AAyGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,oBADH;AAERC,QAAAA,KAAK,EAAE,QAFC;AAGRC,QAAAA,cAAc,EAAE,qBAHR;AAIRC,QAAAA,MAAM,EAAE,MAJA;AAKRC,QAAAA,mBAAmB,EAAE,yBALb;AAMRC,QAAAA,aAAa,EAAE;AANP,OAzGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,mBADf;AAEJ,gBAAQ,2BAFJ;AAGJ,gBAAQ,0BAHJ;AAIJ,eAAO,iBAJH;AAKJ,iBAAS,iBALL;AAMJ,gBAAQ,oBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,oBATb;AAUJ,wBAAgB,iBAVZ;AAWJ,uBAAe,6BAXX;AAYJ,yBAAiB,4BAZb;AAaJ,wBAAgB,4BAbZ;AAcJ,uBAAe,qBAdX;AAeJ,+BAAuB,6BAfnB;AAgBJ,6BAAqB,yBAhBjB;AAiBJ,mBAAW,wBAjBP;AAkBJ,kBAAU,yBAlBN;AAmBJ,sBAAc,yCAnBV;AAoBJ,oBAAY,wCApBR;AAqBJ,oBAAY,wCArBR;AAsBJ,oBAAY,wCAtBR;AAuBJ,oBAAY,wCAvBR;AAwBJ,oBAAY,wCAxBR;AAyBJ,oBAAY,wCAzBR;AA0BJ,gCAAwB,0BA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,UADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-pt-PT.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'pt-PT': {\n      font: {\n        bold: 'Negrito',\n        italic: 'Itálico',\n        underline: 'Sublinhado',\n        clear: 'Remover estilo da fonte',\n        height: '<PERSON><PERSON> da linha',\n        name: '<PERSON><PERSON>',\n        strikethrough: 'Risca<PERSON>',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Tamanho da fonte',\n      },\n      image: {\n        image: 'Imagem',\n        insert: 'Inserir imagem',\n        resizeFull: 'Redimensionar Completo',\n        resizeHalf: 'Redimensionar Metade',\n        resizeQuarter: 'Redimensionar Um Quarto',\n        floatLeft: 'Float Esquerda',\n        floatRight: 'Float Direita',\n        floatNone: 'Sem Float',\n        shapeRounded: 'Forma: Arredondado',\n        shapeCircle: 'Forma: Círculo',\n        shapeThumbnail: 'Forma: Minhatura',\n        shapeNone: 'Forma: Nenhum',\n        dragImageHere: 'Arraste uma imagem para aqui',\n        dropImage: 'Arraste uma imagem ou texto',\n        selectFromFiles: 'Selecione a partir dos arquivos',\n        maximumFileSize: 'Tamanho máximo do fixeiro',\n        maximumFileSizeError: 'Tamanho máximo do fixeiro é maior que o permitido.',\n        url: 'Endereço da imagem',\n        remove: 'Remover Imagem',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vídeo',\n        videoLink: 'Link para vídeo',\n        insert: 'Inserir vídeo',\n        url: 'URL do vídeo?',\n        providers: '(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Inserir ligação',\n        unlink: 'Remover ligação',\n        edit: 'Editar',\n        textToDisplay: 'Texto para exibir',\n        url: 'Que endereço esta licação leva?',\n        openInNewWindow: 'Abrir numa nova janela',\n      },\n      table: {\n        table: 'Tabela',\n        addRowAbove: 'Adicionar linha acima',\n        addRowBelow: 'Adicionar linha abaixo',\n        addColLeft: 'Adicionar coluna à Esquerda',\n        addColRight: 'Adicionar coluna à Esquerda',\n        delRow: 'Excluir linha',\n        delCol: 'Excluir coluna',\n        delTable: 'Excluir tabela',\n      },\n      hr: {\n        insert: 'Inserir linha horizontal',\n      },\n      style: {\n        style: 'Estilo',\n        p: 'Parágrafo',\n        blockquote: 'Citação',\n        pre: 'Código',\n        h1: 'Título 1',\n        h2: 'Título 2',\n        h3: 'Título 3',\n        h4: 'Título 4',\n        h5: 'Título 5',\n        h6: 'Título 6',\n      },\n      lists: {\n        unordered: 'Lista com marcadores',\n        ordered: 'Lista numerada',\n      },\n      options: {\n        help: 'Ajuda',\n        fullscreen: 'Janela Completa',\n        codeview: 'Ver código-fonte',\n      },\n      paragraph: {\n        paragraph: 'Parágrafo',\n        outdent: 'Menor tabulação',\n        indent: 'Maior tabulação',\n        left: 'Alinhar à esquerda',\n        center: 'Alinhar ao centro',\n        right: 'Alinha à direita',\n        justify: 'Justificado',\n      },\n      color: {\n        recent: 'Cor recente',\n        more: 'Mais cores',\n        background: 'Fundo',\n        foreground: 'Fonte',\n        transparent: 'Transparente',\n        setTransparent: 'Fundo transparente',\n        reset: 'Restaurar',\n        resetToDefault: 'Restaurar padrão',\n        cpSelect: 'Selecionar',\n      },\n      shortcut: {\n        shortcuts: 'Atalhos do teclado',\n        close: 'Fechar',\n        textFormatting: 'Formatação de texto',\n        action: 'Ação',\n        paragraphFormatting: 'Formatação de parágrafo',\n        documentStyle: 'Estilo de documento',\n      },\n      help: {\n        'insertParagraph': 'Inserir Parágrafo',\n        'undo': 'Desfazer o último comando',\n        'redo': 'Refazer o último comando',\n        'tab': 'Maior tabulação',\n        'untab': 'Menor tabulação',\n        'bold': 'Colocar em negrito',\n        'italic': 'Colocar em itálico',\n        'underline': 'Colocar em sublinhado',\n        'strikethrough': 'Colocar em riscado',\n        'removeFormat': 'Limpar o estilo',\n        'justifyLeft': 'Definir alinhado à esquerda',\n        'justifyCenter': 'Definir alinhado ao centro',\n        'justifyRight': 'Definir alinhado à direita',\n        'justifyFull': 'Definir justificado',\n        'insertUnorderedList': 'Alternar lista não ordenada',\n        'insertOrderedList': 'Alternar lista ordenada',\n        'outdent': 'Recuar parágrafo atual',\n        'indent': 'Avançar parágrafo atual',\n        'formatPara': 'Alterar formato do bloco para parágrafo',\n        'formatH1': 'Alterar formato do bloco para Título 1',\n        'formatH2': 'Alterar formato do bloco para Título 2',\n        'formatH3': 'Alterar formato do bloco para Título 3',\n        'formatH4': 'Alterar formato do bloco para Título 4',\n        'formatH5': 'Alterar formato do bloco para Título 5',\n        'formatH6': 'Alterar formato do bloco para Título 6',\n        'insertHorizontalRule': 'Inserir linha horizontal',\n        'linkDialog.show': 'Inserir uma ligração',\n      },\n      history: {\n        undo: 'Desfazer',\n        redo: 'Refazer',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}