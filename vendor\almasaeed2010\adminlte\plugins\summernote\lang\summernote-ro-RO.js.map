{"version": 3, "file": "lang/summernote-ro-RO.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,UADF;AAEJC,QAAAA,MAAM,EAAE,UAFJ;AAGJC,QAAAA,SAAS,EAAE,WAHP;AAIJC,QAAAA,KAAK,EAAE,yBAJH;AAKJC,QAAAA,MAAM,EAAE,eALJ;AAMJC,QAAAA,IAAI,EAAE,oBANF;AAOJC,QAAAA,aAAa,EAAE,OAPX;AAQJC,QAAAA,SAAS,EAAE,QARP;AASJC,QAAAA,WAAW,EAAE,UATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,MAAM,EAAE,mBAFH;AAGLC,QAAAA,UAAU,EAAE,yBAHP;AAILC,QAAAA,UAAU,EAAE,qBAJP;AAKLC,QAAAA,aAAa,EAAE,qBALV;AAMLC,QAAAA,SAAS,EAAE,oBANN;AAOLC,QAAAA,UAAU,EAAE,qBAPP;AAQLC,QAAAA,SAAS,EAAE,eARN;AASLC,QAAAA,YAAY,EAAE,eATT;AAULC,QAAAA,WAAW,EAAE,aAVR;AAWLC,QAAAA,cAAc,EAAE,mBAXX;AAYLC,QAAAA,SAAS,EAAE,iBAZN;AAaLC,QAAAA,aAAa,EAAE,kCAbV;AAcLC,QAAAA,SAAS,EAAE,gCAdN;AAeLC,QAAAA,eAAe,EAAE,mBAfZ;AAgBLC,QAAAA,eAAe,EAAE,0BAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,oCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,aAlBA;AAmBLC,QAAAA,MAAM,EAAE,gBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,YAFN;AAGLpB,QAAAA,MAAM,EAAE,iBAHH;AAILgB,QAAAA,GAAG,EAAE,YAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,gBAFJ;AAGJuB,QAAAA,MAAM,EAAE,eAHJ;AAIJC,QAAAA,IAAI,EAAE,UAJF;AAKJC,QAAAA,aAAa,EAAE,sBALX;AAMJT,QAAAA,GAAG,EAAE,iDAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,WAAW,EAAE,sBAFR;AAGLC,QAAAA,WAAW,EAAE,sBAHR;AAILC,QAAAA,UAAU,EAAE,uBAJP;AAKLC,QAAAA,WAAW,EAAE,wBALR;AAMLC,QAAAA,MAAM,EAAE,aANH;AAOLC,QAAAA,MAAM,EAAE,gBAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,GAFE;AAGLC,QAAAA,UAAU,EAAE,OAHP;AAILC,QAAAA,GAAG,EAAE,aAJA;AAKLC,QAAAA,EAAE,EAAE,SALC;AAMLC,QAAAA,EAAE,EAAE,SANC;AAOLC,QAAAA,EAAE,EAAE,SAPC;AAQLC,QAAAA,EAAE,EAAE,SARC;AASLC,QAAAA,EAAE,EAAE,SATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,kBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,QADC;AAEPC,QAAAA,UAAU,EAAE,SAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,kBAFA;AAGTC,QAAAA,MAAM,EAAE,iBAHC;AAITC,QAAAA,IAAI,EAAE,oBAJG;AAKTC,QAAAA,MAAM,EAAE,mBALC;AAMTC,QAAAA,KAAK,EAAE,qBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,iBADH;AAELC,QAAAA,IAAI,EAAE,mBAFD;AAGLC,QAAAA,UAAU,EAAE,qBAHP;AAILC,QAAAA,UAAU,EAAE,mBAJP;AAKLC,QAAAA,WAAW,EAAE,aALR;AAMLC,QAAAA,cAAc,EAAE,qBANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,sBADH;AAERC,QAAAA,KAAK,EAAE,SAFC;AAGRC,QAAAA,cAAc,EAAE,gBAHR;AAIRC,QAAAA,MAAM,EAAE,SAJA;AAKRC,QAAAA,mBAAmB,EAAE,oBALb;AAMRC,QAAAA,aAAa,EAAE,eANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,oBADf;AAEJ,gBAAQ,6BAFJ;AAGJ,gBAAQ,6BAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,uBANJ;AAOJ,kBAAU,uBAPN;AAQJ,qBAAa,wBART;AASJ,yBAAiB,oBATb;AAUJ,wBAAgB,oBAVZ;AAWJ,uBAAe,yBAXX;AAYJ,yBAAiB,yBAZb;AAaJ,wBAAgB,0BAbZ;AAcJ,uBAAe,uBAdX;AAeJ,+BAAuB,2BAfnB;AAgBJ,6BAAqB,yBAhBjB;AAiBJ,mBAAW,oCAjBP;AAkBJ,kBAAU,kCAlBN;AAmBJ,sBAAc,0CAnBV;AAoBJ,oBAAY,oCApBR;AAqBJ,oBAAY,oCArBR;AAsBJ,oBAAY,oCAtBR;AAuBJ,oBAAY,oCAvBR;AAwBJ,oBAAY,oCAxBR;AAyBJ,oBAAY,oCAzBR;AA0BJ,gCAAwB,yBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,mBADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ro-RO.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'ro-RO': {\n      font: {\n        bold: 'Îngroșat',\n        italic: 'Înclinat',\n        underline: 'Subliniat',\n        clear: 'Înlătură formatare font',\n        height: 'Înălțime rând',\n        name: '<PERSON><PERSON><PERSON> de fonturi',\n        strikethrough: 'T<PERSON><PERSON><PERSON>',\n        subscript: 'Indice',\n        superscript: 'Exponent',\n        size: 'Dimensiune font',\n      },\n      image: {\n        image: 'Imagine',\n        insert: 'Inserează imagine',\n        resizeFull: 'Redimensionează complet',\n        resizeHalf: 'Redimensionează 1/2',\n        resizeQuarter: 'Redimensionează 1/4',\n        floatLeft: 'Aliniere la stânga',\n        floatRight: 'Aliniere la dreapta',\n        floatNone: 'Fară aliniere',\n        shapeRounded: 'Formă: Rotund',\n        shapeCircle: 'Formă: Cerc',\n        shapeThumbnail: 'Formă: Pictogramă',\n        shapeNone: 'Formă: Nici una',\n        dragImageHere: 'Trage o imagine sau un text aici',\n        dropImage: 'Eliberează imaginea sau textul',\n        selectFromFiles: 'Alege din fişiere',\n        maximumFileSize: 'Dimensiune maximă fișier',\n        maximumFileSizeError: 'Dimensiune maximă fișier depășită.',\n        url: 'URL imagine',\n        remove: 'Șterge imagine',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Link video',\n        insert: 'Inserează video',\n        url: 'URL video?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion sau Youku)',\n      },\n      link: {\n        link: 'Link',\n        insert: 'Inserează link',\n        unlink: 'Înlătură link',\n        edit: 'Editează',\n        textToDisplay: 'Text ce va fi afişat',\n        url: 'La ce adresă URL trebuie să conducă acest link?',\n        openInNewWindow: 'Deschidere în fereastră nouă',\n      },\n      table: {\n        table: 'Tabel',\n        addRowAbove: 'Adaugă rând deasupra',\n        addRowBelow: 'Adaugă rând dedesubt',\n        addColLeft: 'Adaugă coloană stânga',\n        addColRight: 'Adaugă coloană dreapta',\n        delRow: 'Șterge rând',\n        delCol: 'Șterge coloană',\n        delTable: 'Șterge tabel',\n      },\n      hr: {\n        insert: 'Inserează o linie orizontală',\n      },\n      style: {\n        style: 'Stil',\n        p: 'p',\n        blockquote: 'Citat',\n        pre: 'Preformatat',\n        h1: 'Titlu 1',\n        h2: 'Titlu 2',\n        h3: 'Titlu 3',\n        h4: 'Titlu 4',\n        h5: 'Titlu 5',\n        h6: 'Titlu 6',\n      },\n      lists: {\n        unordered: 'Listă neordonată',\n        ordered: 'Listă ordonată',\n      },\n      options: {\n        help: 'Ajutor',\n        fullscreen: 'Măreşte',\n        codeview: 'Sursă',\n      },\n      paragraph: {\n        paragraph: 'Paragraf',\n        outdent: 'Creşte identarea',\n        indent: 'Scade identarea',\n        left: 'Aliniere la stânga',\n        center: 'Aliniere centrală',\n        right: 'Aliniere la dreapta',\n        justify: 'Aliniere în bloc',\n      },\n      color: {\n        recent: 'Culoare recentă',\n        more: 'Mai multe  culori',\n        background: 'Culoarea fundalului',\n        foreground: 'Culoarea textului',\n        transparent: 'Transparent',\n        setTransparent: 'Setează transparent',\n        reset: 'Resetează',\n        resetToDefault: 'Revino la iniţial',\n      },\n      shortcut: {\n        shortcuts: 'Scurtături tastatură',\n        close: 'Închide',\n        textFormatting: 'Formatare text',\n        action: 'Acţiuni',\n        paragraphFormatting: 'Formatare paragraf',\n        documentStyle: 'Stil paragraf',\n        extraKeys: 'Taste extra',\n      },\n      help: {\n        'insertParagraph': 'Inserează paragraf',\n        'undo': 'Revine la starea anterioară',\n        'redo': 'Revine la starea ulterioară',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Setează stil îngroșat',\n        'italic': 'Setează stil înclinat',\n        'underline': 'Setează stil subliniat',\n        'strikethrough': 'Setează stil tăiat',\n        'removeFormat': 'Înlătură formatare',\n        'justifyLeft': 'Setează aliniere stânga',\n        'justifyCenter': 'Setează aliniere centru',\n        'justifyRight': 'Setează aliniere dreapta',\n        'justifyFull': 'Setează aliniere bloc',\n        'insertUnorderedList': 'Comutare listă neordinată',\n        'insertOrderedList': 'Comutare listă ordonată',\n        'outdent': 'Înlătură indentare paragraf curent',\n        'indent': 'Adaugă indentare paragraf curent',\n        'formatPara': 'Schimbă formatarea selecției în paragraf',\n        'formatH1': 'Schimbă formatarea selecției în H1',\n        'formatH2': 'Schimbă formatarea selecției în H2',\n        'formatH3': 'Schimbă formatarea selecției în H3',\n        'formatH4': 'Schimbă formatarea selecției în H4',\n        'formatH5': 'Schimbă formatarea selecției în H5',\n        'formatH6': 'Schimbă formatarea selecției în H6',\n        'insertHorizontalRule': 'Adaugă linie orizontală',\n        'linkDialog.show': 'Inserează link',\n      },\n      history: {\n        undo: 'Starea anterioară',\n        redo: 'Starea ulterioară',\n      },\n      specialChar: {\n        specialChar: 'CARACTERE SPECIALE',\n        select: 'Alege caractere speciale',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}